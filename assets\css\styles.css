/**
 * <PERSON><PERSON><PERSON> <PERSON><PERSON> bổ sung cho banner
 */
.pulse-icon {
    animation: pulse-icon 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    color: #FFA959;
    text-shadow: 0 0 10px rgba(249, 115, 22, 0.5);
}

@keyframes pulse-icon {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.2);
    }
}

/* Font size mở rộng cho banner */
.text-5\.5xl {
    font-size: 3.5rem !important;
}

/* Hiệu ứng đặc biệt khi hover lên banner badge */
.banner-badge:hover .pulse-icon {
    animation: pulse-highlight 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-highlight {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
        color: var(--primary);
    }

    50% {
        opacity: 1;
        transform: scale(1.4);
        color: #ffffff;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* <PERSON> màu primary */
.shadow-primary\/20 {
    box-shadow: 0 10px 25px rgba(243, 115, 33, 0.2);
}

/* Hiệu ứng background transparent khi hover lên nút có nền màu primary */
.bg-primary.hover\:bg-transparent:hover {
    background-color: transparent;
    color: var(--primary);
}

/* Tối ưu hiển thị banner trên thiết bị nhỏ */
@media (max-width: 640px) {
    .banner-title {
        -webkit-hyphens: auto;
        hyphens: auto;
    }

    .banner-content .feature-item {
        width: 100%;
    }
}

/* Hiệu ứng border gradient */
.border-gradient {
    position: relative;
}

.border-gradient::after {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(to right, var(--primary), transparent);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
}

/* Hiệu ứng hover đặc biệt cho nút CTA */
.cta-button:hover,
.cta-button-alt:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.02em;
}

/* Hiệu ứng phát sáng cho nút CTA */
.cta-button {
    position: relative;
    background: linear-gradient(45deg, #F37321, #FF8A48) !important;
    border-color: rgba(249, 115, 22, 0.5) !important;
}

.cta-button:after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 20px;
    background: var(--primary);
    z-index: -2;
    opacity: 0;
    transition: opacity 0.5s;
    filter: blur(15px);
}

.cta-button:hover:after {
    opacity: 0.5;
    animation: pulsate 2s infinite;
}

@keyframes pulsate {
    0% {
        opacity: 0.4;
        transform: scale(1);
    }

    50% {
        opacity: 0.6;
        transform: scale(1.05);
    }

    100% {
        opacity: 0.4;
        transform: scale(1);
    }
}

/* Hiệu ứng sáng đặc biệt cho nút CTA thứ hai */
.cta-button-alt {
    background: linear-gradient(120deg, rgba(249, 115, 22, 0.1), rgba(255, 255, 255, 0.1)) !important;
    border: 1px solid rgba(249, 115, 22, 0.3) !important;
}

.cta-button-alt:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    top: 0;
    left: -100%;
    transition: left 0.7s;
}

.cta-button-alt:hover {
    background: linear-gradient(120deg, rgba(249, 115, 22, 0.2), rgba(255, 255, 255, 0.1)) !important;
    border-color: rgba(249, 115, 22, 0.5) !important;
}

.cta-button-alt:hover:before {
    left: 100%;
}

/* Hiệu ứng cho các item trong feature list */
.feature-item {
    transition: all 0.3s ease;
    background: linear-gradient(120deg, rgba(249, 115, 22, 0.1), rgba(255, 255, 255, 0.08)) !important;
    border: 1px solid rgba(249, 115, 22, 0.2) !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.feature-item:hover {
    transform: translateY(-3px);
    background: linear-gradient(120deg, rgba(249, 115, 22, 0.15), rgba(255, 255, 255, 0.1)) !important;
    border-color: rgba(249, 115, 22, 0.4) !important;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1), 0 0 10px rgba(249, 115, 22, 0.15);
}

/* Hiệu ứng delay cho animation */
.delay-75 {
    animation-delay: 75ms;
}

.delay-150 {
    animation-delay: 150ms;
}

.delay-300 {
    animation-delay: 300ms;
}

.delay-500 {
    animation-delay: 500ms;
}

/* Hiệu ứng animation ping cho sparkle */
@keyframes ping {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    75%,
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.animate-ping {
    animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Hiệu ứng badge đặc biệt */
.banner-badge {
    transition: all 0.3s ease;
    background: linear-gradient(120deg, rgba(249, 115, 22, 0.15), rgba(255, 255, 255, 0.08)) !important;
    border: 1px solid rgba(249, 115, 22, 0.3) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 0 10px rgba(249, 115, 22, 0.2);
}

.banner-badge:hover {
    transform: translateY(-3px);
    background: linear-gradient(120deg, rgba(249, 115, 22, 0.2), rgba(255, 255, 255, 0.1)) !important;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 0 15px rgba(249, 115, 22, 0.3);
    border-color: rgba(249, 115, 22, 0.5) !important;
}

/* Hiệu ứng gradient text */
.text-gradient {
    background: linear-gradient(90deg, #F37321, #FFB347);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    position: relative;
    white-space: nowrap;
    text-shadow: 0 0 20px rgba(249, 115, 22, 0.25);
}

/* Mô tả chi tiết được làm nổi bật hơn */
.banner-content .mb-6.bg-black\/20 {
    background: linear-gradient(120deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.2)) !important;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), inset 0 0 15px rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.2) !important;
}

.banner-content .mb-6.bg-black\/20:hover {
    background: linear-gradient(120deg, rgba(0, 0, 0, 0.35), rgba(0, 0, 0, 0.25)) !important;
    border-color: rgba(249, 115, 22, 0.3) !important;
}

/* Phần thông tin liên hệ */
.contact-info>div {
    background: linear-gradient(120deg, rgba(249, 115, 22, 0.1), rgba(255, 255, 255, 0.05)) !important;
    border: 1px solid rgba(249, 115, 22, 0.2) !important;
}

/* CSS cho phần danh mục sản phẩm trên trang chủ - Thiết kế mới hiện đại */
.category-card {
    position: relative;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    background: #ffffff;
    height: 100%;
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 30px rgba(0, 0, 0, 0.08);
}

/* Hiệu ứng hover cho hình ảnh */
.category-card .relative img {
    transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.category-card:hover .relative img {
    transform: scale(1.08);
    filter: contrast(1.05);
}

/* Badge số lượng sản phẩm */
.category-card .px-2\.5.py-1\.5 {
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    letter-spacing: 0.01em;
    opacity: 0.9;
}

.category-card:hover .px-2\.5.py-1\.5 {
    transform: translateY(-2px);
    opacity: 1;
    background: var(--primary-color, #F37321);
    color: white;
}

/* Tiêu đề danh mục */
.category-card h3 {
    position: relative;
    transition: all 0.3s ease;
    padding-bottom: 0.5rem;
}

.category-card h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 3rem;
    height: 2px;
    background-color: var(--primary-color, #F37321);
    transform: scaleX(0.3);
    opacity: 0.5;
    transform-origin: 0 0;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.category-card:hover h3:after {
    transform: scaleX(1);
    opacity: 1;
}

/* Mô tả danh mục */
.category-card p {
    transition: all 0.3s ease;
    color: #666;
}

.category-card:hover p {
    color: #333;
}

/* Nút xem chi tiết */
.category-btn {
    overflow: hidden;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.category-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--primary-color, #F37321), var(--primary-color, #F37321));
    z-index: -1;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    opacity: 0;
    transform: scaleX(0);
    transform-origin: 0 50%;
}

.category-card:hover .category-btn:before {
    opacity: 1;
    transform: scaleX(1);
}

.category-card:hover .category-btn {
    color: white;
    border-color: var(--primary-color, #F37321);
}

/* Icon trong nút */
.category-btn-icon {
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    box-shadow: 0 0 0 4px rgba(243, 115, 33, 0.1);
}

.category-card:hover .category-btn-icon {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateX(3px);
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.2);
}

/* Hiệu ứng loading skeleton cho cards */
@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }

    100% {
        background-position: 1000px 0;
    }
}

.category-card.loading::before {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.08) 8%, rgba(0, 0, 0, 0.15) 18%, rgba(0, 0, 0, 0.08) 33%);
    background-size: 1000px 100%;
    animation: shimmer 1.5s infinite linear;
}

/* Hỗ trợ màn hình nhỏ */
@media (max-width: 640px) {
    .category-card .h-44 {
        height: 140px;
    }

    .category-card h3 {
        font-size: 1rem;
    }

    .category-card .p-5 {
        padding: 1rem;
    }

    .category-card p {
        font-size: 0.75rem;
        line-height: 1.3;
    }

    .category-btn {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .category-btn-icon {
        width: 1.25rem;
        height: 1.25rem;
    }
}

/* Đảm bảo phần tử cha có vị trí tương đối để các phần tử con có thể định vị tuyệt đối */
.bg-primary\/10 {
    background-color: rgba(243, 115, 33, 0.1);
}

.text-primary {
    color: var(--primary-color, #F37321);
}

.bg-primary\/80 {
    background-color: rgba(243, 115, 33, 0.8);
}

.bg-primary {
    background-color: var(--primary-color, #F37321);
}

/* Hiệu ứng cho phần heading */
.text-3xl.md\:text-4xl.font-bold .absolute {
    transition: transform 0.3s ease;
}

.text-3xl.md\:text-4xl.font-bold:hover .absolute {
    transform: rotate(-2deg) translateY(2px);
}

/* Hiệu ứng hover cho "Xem tất cả danh mục" */
.container .text-center.mt-12 a:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* CSS cho phần sản phẩm nổi bật slider */
.featured-products-slider {
    position: relative;
    padding: 10px 10px 0 10px;
    margin: 0 -10px;
}

.featured-products-slider .swiper-container {
    overflow: visible;
    padding: 20px 0;
}

/* ✨ Tối ưu cho touch interaction - Slide containers */
.featured-products-slider .swiper-container,
.category-products-slider .swiper-container {
    touch-action: pan-y pinch-zoom;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-touch-callout: none;
}

/* ✨ Tối ưu cho slide items */
.featured-products-slider .swiper-slide,
.category-products-slider .swiper-slide {
    touch-action: manipulation;
    cursor: grab;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

.featured-products-slider .swiper-slide:active,
.category-products-slider .swiper-slide:active {
    cursor: grabbing;
}

/* ✨ Tối ưu cho links trong slides */
.swiper-slide a {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* ✨ Ngăn text selection khi drag */
.swiper-slide * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
}

/* CSS cho featured-swiper */
.featured-swiper,
.swiper.featured-swiper,
.swiper.featured-swiper.swiper-initialized.swiper-horizontal.swiper-backface-hidden {
    padding-top: 10px !important;
}

.featured-products-slider .swiper-slide {
    height: auto;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    opacity: 1;
    transform: scale(1);
}

.featured-products-slider .swiper-slide-active,
.featured-products-slider .swiper-slide-next,
.featured-products-slider .swiper-slide-prev {
    /* Không có quy tắc đặc biệt nào - tất cả slide như nhau */
}

/* Card sản phẩm trong slider */
.featured-products-slider .product-card {
    position: relative;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    height: 100%;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.featured-products-slider .product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
}

/* Ảnh sản phẩm */
.featured-products-slider .product-image-container {
    position: relative;
    overflow: hidden;
}

.featured-products-slider .product-image-container img {
    transition: all 0.7s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.featured-products-slider .product-card:hover .product-image-container img {
    transform: scale(1.1);
}

/* Badge giảm giá */
.featured-products-slider .bg-red-500 {
    box-shadow: 0 4px 10px rgba(239, 68, 68, 0.3);
    animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

/* Nút thêm vào giỏ hàng nhanh */
.quick-add-to-cart-btn {
    transform: translateY(70px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    z-index: 10;
}

.product-card:hover .quick-add-to-cart-btn {
    transform: translateY(0);
    opacity: 1;
}

.quick-add-to-cart-btn:hover {
    transform: translateY(-3px);
}

/* Trạng thái còn hàng */
.featured-products-slider .bg-green-100 {
    transition: all 0.3s ease;
}

.product-card:hover .bg-green-100 {
    background-color: rgba(16, 185, 129, 0.2);
}

/* Đánh giá sao */
.featured-products-slider .fa-star {
    transition: all 0.3s ease;
}

.product-card:hover .fa-star {
    transform: scale(1.1);
    color: #FFB800;
}

/* Giá sản phẩm */
.featured-products-slider .price-container {
    transition: all 0.3s ease;
}

.product-card:hover .price-container .text-primary {
    transform: scale(1.05);
}

/* Nút điều hướng slider */
.featured-slider-navigation {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    position: relative;
    z-index: 20;
}

.slider-nav-button {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background-color: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--primary-color, #F37321);
    border: 1px solid rgba(243, 115, 33, 0.1);
}

.slider-nav-button:hover {
    background-color: var(--primary-color, #F37321);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 15px rgba(243, 115, 33, 0.2);
}

/* Phân trang dots */
.featured-slider-pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.featured-slider-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background-color: rgba(243, 115, 33, 0.3);
    opacity: 1;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.featured-slider-pagination .swiper-pagination-bullet-active {
    background-color: var(--primary-color, #F37321);
    width: 24px;
}

/* ✨ Tối ưu riêng cho touch devices */
@media (hover: none) and (pointer: coarse) {
    .swiper-slide {
        cursor: default !important;
    }

    .swiper-slide:active {
        cursor: default !important;
    }

    /* Giảm sensitivity cho touch */
    .featured-products-slider .swiper-container,
    .category-products-slider .swiper-container {
        touch-action: pan-x pan-y;
    }

    /* Tối ưu cho mobile touch */
    .swiper-slide a {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        tap-highlight-color: rgba(0, 0, 0, 0.1);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .featured-products-slider .swiper-slide {
        opacity: 1;
        transform: scale(1);
    }

    .featured-slider-navigation {
        position: absolute;
        width: 100%;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        justify-content: space-between;
        padding: 0 10px;
        z-index: 20;
        pointer-events: none;
    }

    .slider-nav-button {
        width: 2.5rem;
        height: 2.5rem;
        pointer-events: auto;
    }

    .featured-button-prev {
        margin-left: -10px;
    }

    .featured-button-next {
        margin-right: -10px;
    }

    /* ✨ Tối ưu touch cho mobile */
    .swiper-slide {
        touch-action: pan-x;
    }

    .swiper-slide a {
        display: block;
        -webkit-tap-highlight-color: transparent;
    }
}

/* Hiệu ứng cho nút thêm vào giỏ nhanh khi click */
.quick-add-to-cart-btn.adding {
    animation: rotating 1s linear infinite;
    background-color: var(--primary-color, #F37321);
    color: white;
}

@keyframes rotating {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* Hiệu ứng pulse khi thêm sản phẩm vào giỏ hàng - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT */
.pulse-once {
    /* animation: pulse-once 0.5s cubic-bezier(0.66, 0, 0, 1); */
}

@keyframes pulse-once {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.3);
    }

    100% {
        transform: scale(1);
    }
}

/* Hiệu ứng loading cho card sản phẩm khi chưa tải xong */
.featured-products-slider .product-card {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease;
}

/* Tooltip hiển thị khi hover vào nút thêm vào giỏ */
.quick-add-to-cart-btn {
    position: relative;
}

.quick-add-to-cart-btn::before {
    content: "Thêm vào giỏ";
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%) scale(0.8);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.quick-add-to-cart-btn::after {
    content: "";
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%) scale(0.8);
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.quick-add-to-cart-btn:hover::before,
.quick-add-to-cart-btn:hover::after {
    opacity: 1;
    transform: translateX(-50%) scale(1);
}