<?php
// Thiết lập tiêu đề trang
$page_title = 'Trang chủ';
$page_description = 'Nội Thất Băng Vũ cung cấp các sản phẩm nội thất cao cấp, chất lượng với giá thành hợp lý';
$body_class = 'homepage'; // Thêm class để phân biệt trang chủ

// Include header
include_once 'partials/header.php';

// Include helper cho nút
include_once 'includes/button-helper.php';

// Lấy sản phẩm nổi bật (chỉ lấy sản phẩm có trạng thái hiển thị)
$featured_products = get_products(8, 0, null, 1, null, 1);

// Lấy danh mục hiển thị ở trang chủ
$homepage_categories = get_homepage_categories(1);

// Lấy tất cả banner đang hoạt động
$banners = get_active_banners(1);


?>

<!-- Link CSS Swiper -->
<link rel="stylesheet" href="https://unpkg.com/swiper@7/swiper-bundle.min.css" />

<!-- Banner Staggered Animation CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/banner-staggered-animation.css">

<!-- CSS cho thông báo đơn giản -->
<style>
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -20px);
        }

        to {
            opacity: 1;
            transform: translate(-50%, 0);
        }
    }
</style>

<!-- Link CSS cho search page (để sử dụng styling cho product cards) -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/search-page.css" />

<!-- Link CSS cho index page (để override styling cho product cards) -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/index.css" />

<!-- Link CSS cho trang chủ (load cuối để override search-page.css) -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/homepage.css?v=<?php echo time(); ?>" />

<!-- Link CSS cho phần Dịch vụ thiết kế -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/design-services.css" />

<!-- Link CSS cho phần Giới thiệu công ty -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/company-intro.css" />

<!-- Link CSS cho phần Đối tác chính thức -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/official-partners.css" />

<!-- Link CSS cho phần Cảm nhận khách hàng -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/testimonials.css" />

<!-- Link CSS cho hiệu ứng scroll sản phẩm -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/product-scroll-animations.css" />

<!-- Link CSS cho Fancybox -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css" />

<!-- CSS tùy chỉnh cho Fancybox -->
<style>
    /* Style cho thumbnails trong Fancybox */
    .fancybox__thumbs {
        padding: 5px 0;
        background: rgba(0, 0, 0, 0.8);
    }

    .fancybox__thumbs .carousel__slide {
        margin: 0 2px;
    }

    .fancybox__thumb {
        width: 70px;
        height: 50px;
        opacity: 0.7;
        transition: opacity 0.2s ease, border 0.2s ease;
        border: 2px solid transparent;
    }

    .fancybox__thumb:hover {
        opacity: 1;
    }

    .is-nav-selected .fancybox__thumb {
        opacity: 1;
        border-color: var(--primary-color, #F37321);
    }

    /* Style cho container testimonials */
    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        gap: 24px;
    }

    @media (min-width: 768px) {
        .testimonials-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (min-width: 1024px) {
        .testimonials-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    /* Style cho phần ảnh đánh giá */
    .testimonial-media-link {
        display: block;
        width: 100%;
        height: 100%;
        position: relative;
        cursor: pointer;
        overflow: hidden;
        transition: transform 0.3s ease;
    }

    .testimonial-media-link:hover {
        transform: scale(1.05);
    }

    .testimonial-media-link img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    /* Hiển thị ảnh đánh giá trên một hàng */
    .testimonial-photo-item {
        position: relative;
        width: 80px;
        height: 80px;
        margin-right: 5px;
        border-radius: 4px;
        overflow: hidden;
        flex: 0 0 auto;
    }



    /* Tùy chỉnh scrollbar cho desktop */
    .testimonial-photos::-webkit-scrollbar {
        height: 4px;
    }

    .testimonial-photos::-webkit-scrollbar-thumb {
        background-color: rgba(243, 115, 33, 0.5);
        border-radius: 4px;
    }

    /* Responsive cho phần Cảm nhận khách hàng */
    .testimonials-section {
        position: relative;
        padding: 60px 0;
        background-color: #f9f9f9;
        overflow: hidden;
    }

    .testimonials-title-container {
        text-align: center;
        margin-bottom: 40px;
    }

    .testimonials-badge {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        background: rgba(243, 115, 33, 0.1);
        color: var(--primary-color, #F37321);
        border-radius: 50px;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 16px;
    }

    .testimonials-badge .badge-icon {
        margin-right: 8px;
    }

    .testimonials-heading {
        font-size: 32px;
        font-weight: 700;
        color: #1F2937;
        margin-bottom: 16px;
    }

    .testimonials-description {
        font-size: 16px;
        color: #6B7280;
        max-width: 700px;
        margin: 0 auto;
    }

    .testimonial-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        padding: 24px;
        margin-bottom: 0;
        /* Không cần margin bottom vì đã có gap trong grid */
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        /* Đảm bảo cùng chiều cao */
        display: flex;
        flex-direction: column;
    }

    .testimonial-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .testimonial-content {
        color: #4B5563;
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 20px;
        position: relative;
        flex-grow: 1;
        /* Mở rộng để lấp đầy không gian */
    }

    .testimonial-content::before {
        content: '"';
        font-size: 60px;
        color: rgba(243, 115, 33, 0.1);
        position: absolute;
        top: -20px;
        left: -10px;
        font-family: serif;
    }

    .testimonial-customer {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
    }

    .testimonial-photo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid #E5E7EB;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .testimonial-photo img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .testimonial-info {
        margin-left: 16px;
    }

    .testimonial-name {
        font-size: 18px;
        font-weight: 600;
        color: #1F2937;
        margin: 0 0 4px 0;
    }

    .testimonial-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        font-size: 14px;
        color: #6B7280;
        margin-bottom: 6px;
    }

    .testimonial-location,
    .testimonial-age {
        display: flex;
        align-items: center;
    }

    .testimonial-location i,
    .testimonial-age i {
        margin-right: 4px;
        color: var(--primary-color, #F37321);
    }

    .testimonial-rating {
        color: #FBC02D;
        font-size: 14px;
        margin-bottom: 8px;
    }

    .testimonial-rating i {
        margin-right: 2px;
        transition: transform 0.3s ease;
    }

    .testimonial-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
    }

    .testimonial-tag {
        font-size: 12px;
        background-color: #F3F4F6;
        color: #374151;
        padding: 4px 8px;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
    }

    .testimonial-tag i {
        margin-right: 4px;
        color: var(--primary-color, #F37321);
    }

    .testimonial-media {
        margin-top: 16px;
    }



    /* Responsive Media Queries */
    @media (max-width: 1199px) {
        .testimonial-photo-item {
            width: 75px;
            height: 75px;
        }
    }

    @media (max-width: 991px) {
        .testimonials-heading {
            font-size: 28px;
        }

        .testimonial-content {
            font-size: 15px;
        }
    }

    @media (max-width: 767px) {
        .testimonials-section {
            padding: 40px 0;
        }

        .testimonials-heading {
            font-size: 24px;
        }

        .testimonials-badge {
            font-size: 12px;
            padding: 6px 12px;
        }

        .testimonial-card {
            padding: 20px;
        }

        .testimonial-photo {
            width: 50px;
            height: 50px;
        }

        .testimonial-name {
            font-size: 16px;
        }

        .testimonial-meta,
        .testimonial-rating {
            font-size: 12px;
        }

        .testimonials-grid {
            gap: 16px;
        }
    }

    /* Cải thiện giao diện điện thoại */
    @media (max-width: 575px) {

        /* Cải thiện phần tiêu đề */
        .testimonials-section {
            padding: 30px 0;
        }

        .testimonials-title-container {
            margin-bottom: 25px;
            padding: 0 10px;
        }

        .testimonials-heading {
            font-size: 22px;
            margin-bottom: 10px;
        }

        .testimonials-description {
            font-size: 14px;
            line-height: 1.4;
        }

        /* Tối ưu card */
        .testimonial-card {
            padding: 16px;
            margin-bottom: 0;
            border-radius: 6px;
        }

        .testimonial-content {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .testimonial-content::before {
            font-size: 40px;
            top: -15px;
            left: -5px;
        }

        /* Cải thiện layout khách hàng */
        .testimonial-customer {
            margin-bottom: 12px;
            flex-direction: row;
            /* Giữ layout ngang */
            align-items: center;
        }

        .testimonial-photo {
            width: 45px;
            height: 45px;
            border-width: 1px;
            flex-shrink: 0;
        }

        .testimonial-info {
            margin-left: 12px;
            margin-top: 0;
        }

        .testimonial-name {
            font-size: 15px;
            margin-bottom: 2px;
        }

        .testimonial-meta {
            margin-bottom: 4px;
            font-size: 11px;
        }

        .testimonial-location i,
        .testimonial-age i {
            font-size: 11px;
        }

        .testimonial-rating {
            font-size: 12px;
            margin-bottom: 5px;
        }

        .testimonial-tag {
            font-size: 10px;
            padding: 2px 6px;
        }

        /* Tối ưu phần hiển thị ảnh */
        .testimonial-photos {
            margin-top: 10px;
            width: 100%;
            overflow-x: hidden;
            /* Ngăn cuộn ngang */
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            /* Hiển thị 4 ảnh trên một hàng */
            gap: 5px;
            /* Khoảng cách giữa các ảnh */
        }

        /* Ẩn nút điều hướng Swiper trên mobile */
        .swiper-button-next,
        .swiper-button-prev,
        .swiper-button-next-custom,
        .swiper-button-prev-custom {
            display: none !important;
        }

        /* Đảm bảo ảnh hiển thị đúng kích thước */
        .testimonial-photo-item {
            width: 100%;
            height: 0;
            padding-bottom: 100%;
            /* Tạo tỷ lệ 1:1 */
            position: relative;
            overflow: hidden;
        }

        .testimonial-photo-item img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }



        /* Cải thiện grid hiển thị */
        .testimonials-grid {
            gap: 12px;
        }
    }

    /* Thêm điều chỉnh cho màn hình siêu nhỏ */
    @media (max-width: 375px) {
        .testimonials-heading {
            font-size: 20px;
        }

        .testimonials-badge {
            font-size: 11px;
            padding: 5px 10px;
        }

        .testimonial-card {
            padding: 14px;
        }

        .testimonial-content {
            font-size: 13px;
        }

        .testimonial-photo {
            width: 40px;
            height: 40px;
        }

        .testimonial-name {
            font-size: 14px;
        }

        /* Tăng tính dễ sử dụng trên màn hình nhỏ */

        .testimonials-grid {
            gap: 10px;
        }


    }

    /* CSS cho Swiper Testimonials */
    .testimonials-swiper {
        width: 100%;
        padding-bottom: 60px;
        /* Để chừa chỗ cho pagination */
    }

    /* CSS cho overlay "+X ảnh" nhẹ nhàng, hiện đại - PHÓNG TO THEO ẢNH */
    .photo-overlay {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: rgba(0, 0, 0, 0.45) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 10 !important;
        border-radius: 8px !important;
        pointer-events: none !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    /* Overlay phóng to theo ảnh khi hover */
    .testimonial-photo-link:hover .photo-overlay {
        background: rgba(0, 0, 0, 0.6) !important;
        transform: scale(1.05) !important;
    }

    .overlay-text {
        color: white !important;
        font-size: 0.85rem !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
        letter-spacing: 0.3px !important;
        text-align: center !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        line-height: 1.2 !important;
        opacity: 0.95 !important;
    }

    /* ĐỒng nhất border-radius cho ảnh và overlay */
    .testimonial-photo-link img {
        border-radius: 8px !important;
    }

    .testimonial-photo-box {
        border-radius: 8px !important;
    }

    /* Responsive cho overlay text và border-radius */
    @media (max-width: 768px) {
        .overlay-text {
            font-size: 0.75rem !important;
        }

        .testimonial-photo-link img,
        .testimonial-photo-box,
        .photo-overlay {
            border-radius: 6px !important;
        }
    }

    @media (max-width: 480px) {
        .overlay-text {
            font-size: 0.7rem !important;
        }

        .testimonial-photo-link img,
        .testimonial-photo-box,
        .photo-overlay {
            border-radius: 4px !important;
        }
    }

    .testimonials-swiper .swiper-slide {
        height: auto;
        /* Cho phép slide có chiều cao tự động */
        display: flex;
    }

    .testimonials-swiper .testimonial-card {
        width: 100%;
        margin: 0;
        height: 100%;
    }



    /* Tùy chỉnh nút điều hướng */
    .testimonials-button-next,
    .testimonials-button-prev {
        color: var(--primary-color, #F37321);
        --swiper-navigation-size: 30px;
    }

    .testimonials-button-next:after,
    .testimonials-button-prev:after {
        font-weight: bold;
    }

    /* Tùy chỉnh pagination */
    .testimonials-pagination {
        bottom: 10px !important;
    }

    .testimonials-pagination .swiper-pagination-bullet {
        width: 10px;
        height: 10px;
        background: #ccc;
        opacity: 0.5;
    }

    .testimonials-pagination .swiper-pagination-bullet-active {
        background: var(--primary-color, #F37321);
        opacity: 1;
        width: 30px;
        border-radius: 5px;
    }

    /* Responsive cho Swiper */
    @media (max-width: 767px) {

        .testimonials-button-next,
        .testimonials-button-prev {
            display: none !important;
        }

        .testimonials-swiper {
            padding-bottom: 40px;
        }
    }
</style>

<!-- CSS tùy chỉnh cho slider, pagination và tiêu đề -->
<style>
    /* CSS cho pagination của banner */
    .banner-bullet {
        display: inline-block;
        width: 12px !important;
        height: 12px !important;
        border-radius: 50% !important;
        background-color: rgba(255, 255, 255, 0.3) !important;
        margin: 0 4px !important;
        cursor: pointer;
        transition: all 0.3s ease;
        opacity: 1 !important;
    }

    .banner-bullet.swiper-pagination-bullet-active {
        background-color: var(--primary-color, #F37321) !important;
        width: 32px !important;
        border-radius: 6px !important;
    }

    .banner-pagination-container {
        position: absolute;
        bottom: 20px;
        left: 0;
        width: 100%;
        text-align: center;
        z-index: 10;
    }

    /* CSS cho pagination của sản phẩm nổi bật */
    .featured-pagination-bullet {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: rgba(243, 115, 33, 0.3);
        margin: 0 4px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .featured-pagination-bullet-active {
        background-color: var(--primary-color, #F37321);
        width: 24px;
        border-radius: 5px;
    }

    /* CSS cho nút thêm vào giỏ hàng - luôn hiển thị */
    .quick-add-to-cart-btn {
        z-index: 10;
        transition: all 0.3s ease;
        opacity: 1 !important;
        /* Đảm bảo luôn hiển thị */
        transform: translateY(0) !important;
        /* Đảm bảo luôn hiển thị */
    }

    .quick-add-to-cart-btn:hover {
        transform: translateY(-3px) !important;
        box-shadow: 0 10px 15px rgba(243, 115, 33, 0.2);
    }

    /* Hiệu ứng pulse cho số lượng giỏ hàng - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT */
    .pulse-once {
        /* animation: pulse-once 0.5s cubic-bezier(0.66, 0, 0, 1); */
    }

    /* Hiệu ứng nhẹ cho giỏ hàng khi thêm sản phẩm - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT */
    .cart-bounce {
        /* animation: cart-bounce 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) !important; */
        transform-origin: center center;
    }

    /* @keyframes cart-bounce {
        0%, 100% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-5px);
        }
    }

    @keyframes pulse-once {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.3);
        }
        100% {
            transform: scale(1);
        }
    } */

    /* Hiệu ứng khi click vào nút */
    .button-clicked {
        animation: button-click 0.3s ease;
    }

    @keyframes button-click {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(0.95);
        }

        100% {
            transform: scale(1);
        }
    }

    /* Tỉ lệ 1:1 cho phần hình ảnh sản phẩm */
    .product-image-container {
        position: relative;
        width: 100%;
        padding-bottom: 100%;
        /* Tạo tỉ lệ 1:1 */
        overflow: hidden;
    }

    .product-image-container a {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .product-image-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* CSS cho thông báo hiện đại đã được chuyển sang file notifications.css */

    /* Hiệu ứng float lên trên cho card sản phẩm nổi bật */
    .product-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        backface-visibility: hidden;
        will-change: transform, box-shadow;
    }

    .product-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* CSS cho thông tin lượt bán và lượt xem */
    .product-card .flex.items-center.justify-between.mb-3 {
        font-size: 0.75rem;
        color: #6b7280;
    }

    .product-card .flex.items-center.justify-between.mb-3 i {
        margin-right: 0.25rem;
    }

    .product-card .flex.items-center.justify-between.mb-3 .fa-shopping-cart {
        color: #10b981;
    }

    .product-card .flex.items-center.justify-between.mb-3 .fa-eye {
        color: #3b82f6;
    }

    /* CSS chung cho tất cả các phần trên trang chủ */
    .section-spacing {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    @media (min-width: 768px) {
        .section-spacing {
            padding-top: 4rem;
            padding-bottom: 4rem;
        }
    }

    /* CSS chung cho tất cả các tiêu đề chính */
    .section-title-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        background: rgba(243, 115, 33, 0.1);
        color: #F37321;
        font-weight: 600;
        border-radius: 9999px;
        margin-bottom: 1rem;
        font-size: 0.875rem;
    }

    .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: #1F2937;
        margin-bottom: 1rem;
        position: relative;
        display: inline-block;
    }

    .section-title-underline {
        position: absolute;
        bottom: -0.375rem;
        left: 0;
        width: 100%;
        height: 0.25rem;
        background: rgba(243, 115, 33, 0.3);
        border-radius: 9999px;
    }

    .section-description {
        max-width: 36rem;
        margin: 0 auto;
        color: #6B7280;
        font-size: 1rem;
        line-height: 1.5;
    }

    @media (min-width: 768px) {
        .section-title {
            font-size: 2.25rem;
        }
    }

    @media (min-width: 1024px) {
        .section-title {
            font-size: 2.5rem;
        }
    }

    /* Điều chỉnh khoảng cách giữa các phần */
    .section-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
        .section-container {
            margin-top: 3rem;
            margin-bottom: 3rem;
        }
    }

    /* CSS hover cho thẻ sản phẩm trang chủ - Đảm bảo Tailwind classes hoạt động */
    .swiper-slide .group.h-full.flex.flex-col.bg-white.rounded-2xl {
        transition: all 0.5s ease !important;
    }

    .swiper-slide .group.h-full.flex.flex-col.bg-white.rounded-2xl:hover {
        transform: translateY(-8px) !important;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
        border-color: rgb(147 197 253) !important;
    }

    /* Đảm bảo image hover hoạt động */
    .swiper-slide .group:hover img {
        transform: scale(1.1) !important;
    }

    /* CSS cho nút "Xem chi tiết" danh mục - Thiết kế sang trọng */
    .luxury-category-btn {
        display: inline-flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 0.875rem 1.5rem;
        background: linear-gradient(135deg, #ffffff, #f8fafc);
        color: #1d4ed8;
        text-decoration: none;
        border: 1px solid #dbeafe;
        border-radius: 0.875rem;
        font-weight: 600;
        font-size: 0.9375rem;
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(29, 78, 216, 0.12);
        z-index: 1;
    }

    .luxury-category-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #eff6ff, #dbeafe);
        opacity: 0;
        transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: -1;
    }

    .luxury-category-btn:hover::before {
        opacity: 1;
    }

    .luxury-category-btn:hover {
        color: #3b82f6;
        border-color: #bfdbfe;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    }

    .luxury-category-btn .btn-text {
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
    }

    .luxury-category-btn .btn-icon-container {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        background: rgba(29, 78, 216, 0.12);
        border-radius: 50%;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .luxury-category-btn:hover .btn-icon-container {
        background: rgba(59, 130, 246, 0.15);
        transform: translateX(4px) scale(1.1);
    }

    .luxury-category-btn .btn-icon {
        font-size: 0.75rem;
        transition: all 0.3s ease;
    }

    .luxury-category-btn:hover .btn-icon {
        transform: translateX(2px);
    }

    /* Hiệu ứng shine */
    .luxury-category-btn .btn-shine {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.6s ease;
        z-index: 1;
    }

    .luxury-category-btn:hover .btn-shine {
        left: 100%;
    }

    /* Active state */
    .luxury-category-btn:active {
        transform: translateY(-1px) scale(0.98);
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
    }

    /* Focus state */
    .luxury-category-btn:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }

    /* CSS cho text overlay danh mục - giữ màu trắng, tăng độ đậm nền khi hover */
    .group h3 {
        color: white !important;
    }

    .group:hover h3 {
        color: white !important;
    }

    /* Overlay gradient mượt mà cho danh mục - Giống file test */
    .category-overlay {
        background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.6) 0%,
            rgba(0, 0, 0, 0.2) 50%,
            transparent 100%
        ) !important;
        z-index: 5 !important;
        pointer-events: none;
        transition: all 0.3s ease;
    }

    .group:hover .category-overlay {
        background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.3) 50%,
            transparent 100%
        ) !important;
    }

    /* Custom text shadow cho tiêu đề danh mục - Override Tailwind drop-shadow-md */
    .group h3.text-white.drop-shadow-md,
    .group h3.font-bold.text-white,
    .group .absolute h3.text-white {
        text-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
        filter: none !important; /* Tắt drop-shadow của Tailwind */
        --tw-drop-shadow: none !important; /* Reset Tailwind variable */
    }

    /* Hiệu ứng hover cho text shadow */
    .group:hover h3.text-white.drop-shadow-md,
    .group:hover h3.font-bold.text-white,
    .group:hover .absolute h3.text-white {
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }

    /* Responsive - Giảm intensity trên mobile để tối ưu performance */
    @media (max-width: 640px) {
        .group h3.text-white.drop-shadow-md,
        .group h3.font-bold.text-white,
        .group .absolute h3.text-white {
            text-shadow: 0 4px 6px rgba(0, 0, 0, 0.12) !important;
        }

        .group:hover h3.text-white.drop-shadow-md,
        .group:hover h3.font-bold.text-white,
        .group:hover .absolute h3.text-white {
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.18) !important;
        }
    }

    /* Responsive layout cho danh mục - 1 thẻ/hàng khi màn hình ≤ 540px */
    @media (max-width: 540px) {
        .category-grid {
            grid-template-columns: 1fr !important;
        }
    }

    /* FORCE OVERRIDE - Highest specificity để override Tailwind drop-shadow-md */
    body .group .absolute.bottom-0 h3.text-white.drop-shadow-md,
    body .group div.absolute h3.text-white,
    body div.group h3.text-white {
        text-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
        filter: none !important;
        --tw-drop-shadow: none !important;
        -webkit-filter: none !important;
    }

    body .group:hover .absolute.bottom-0 h3.text-white.drop-shadow-md,
    body .group:hover div.absolute h3.text-white,
    body div.group:hover h3.text-white {
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }
</style>

<!-- Banner Slideshow -->
<div class="banner-section relative">
    <?php if (count($banners) > 0): ?>
        <div class="banner-slider">
            <div class="swiper-container banner-swiper">
                <div class="swiper-wrapper">
                    <?php foreach ($banners as $banner): ?>
                        <div class="swiper-slide">
                            <div class="banner-slide-inner relative w-full overflow-hidden">
                                <?php if (isset($banner['media_type']) && $banner['media_type'] === 'video'): ?>
                                    <!-- Video Banner -->
                                    <div class="absolute inset-0 w-full h-full">
                                        <video
                                            class="w-full h-full object-cover banner-video"
                                            muted
                                            playsinline
                                            preload="auto"
                                            poster="<?php echo BASE_URL; ?>/uploads/banners/<?php echo $banner['video_thumbnail']; ?>"
                                            data-video-url="<?php echo htmlspecialchars($banner['video_url']); ?>">
                                            <source src="<?php echo htmlspecialchars($banner['video_url']); ?>" type="video/mp4">
                                            <!-- Fallback cho trình duyệt không hỗ trợ video -->
                                            <img src="<?php echo BASE_URL; ?>/uploads/banners/<?php echo $banner['video_thumbnail']; ?>"
                                                alt="Banner" class="w-full h-full object-cover">
                                        </video>

                                        <!-- Overlay Gradient cho Video Banner - Chỉ hiển thị với banner mặc định -->
                                        <?php if (isset($banner['banner_type']) && $banner['banner_type'] === 'default'): ?>
                                            <div class="absolute inset-0" style="background: linear-gradient(to right, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.6) 50%, transparent 100%); z-index: 1;"></div>
                                        <?php endif; ?>


                                    </div>
                                <?php else: ?>
                                    <!-- Hình ảnh Banner -->
                                    <div class="absolute inset-0 w-full h-full">
                                        <img src="<?php echo BASE_URL; ?>/uploads/banners/<?php echo $banner['image']; ?>"
                                            alt="Banner" class="w-full h-full object-cover">
                                    </div>
                                <?php endif; ?>

                                <?php if (isset($banner['banner_type']) && $banner['banner_type'] === 'default'): ?>
                                    <!-- Overlay Gradient cho Banner Mặc định - Gradient cải tiến, đậm hơn bên trái, mờ dần sang phải -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-black/85 via-black/60 to-transparent"></div>

                                    <!-- Nội dung cho Banner Mặc định - Thiết kế hiện đại cải tiến -->
                                    <div class="container mx-auto px-4 h-full relative z-10">
                                        <div class="flex flex-col justify-center h-full max-w-3xl">
                                            <div class="banner-content text-white transform transition-all duration-1000">
                                                <!-- Badge trên tiêu đề - Thiết kế hiện đại nổi bật hơn -->
                                                <div
                                                    class="banner-badge stagger-item inline-flex items-center bg-white/10 text-white text-sm font-medium px-5 py-2 rounded-full mb-6 backdrop-blur-md border border-white/20 shadow-glow-light hover:bg-white/15 transition-all duration-300 cursor-default relative overflow-hidden">
                                                    <!-- Hiệu ứng sparkle cho badge -->
                                                    <span class="absolute inset-0 -z-10">
                                                        <span
                                                            class="absolute animate-ping w-1 h-1 rounded-full bg-primary/60 top-1/4 left-1/4"></span>
                                                        <span
                                                            class="absolute animate-ping delay-75 w-1.5 h-1.5 rounded-full bg-primary/60 bottom-1/4 right-1/3"></span>
                                                        <span
                                                            class="absolute animate-ping delay-150 w-1 h-1 rounded-full bg-primary/60 top-1/2 right-1/4"></span>
                                                    </span>
                                                    <i class="fas fa-award text-primary mr-2 pulse-icon"></i>
                                                    <?php echo htmlspecialchars($banner['badge_text'] ?? 'Nội Thất Cao Cấp Bàng Vũ'); ?>
                                                </div>

                                                <!-- Tiêu đề chính - Thiết kế hiện đại (hai dòng rõ ràng, kích thước lớn hơn) -->
                                                <h2
                                                    class="banner-title stagger-item text-4xl md:text-5xl lg:text-5.5xl font-bold mb-4 md:mb-6 leading-tight">
                                                    <div class="text-gradient" style="white-space: nowrap;">
                                                        <?php echo htmlspecialchars($banner['title_text'] ?? 'Thiết Kế – Thi Công'); ?>
                                                    </div>
                                                    <div class="mt-1 md:mt-2">
                                                        <?php echo htmlspecialchars($banner['subtitle_text'] ?? 'Nội Thất Theo Yêu Cầu'); ?>
                                                    </div>
                                                </h2>

                                                <!-- Dòng mô tả phụ - Thiết kế hiện đại (hiển thị dạng 3 cột trên desktop, stack trên mobile) -->
                                                <div
                                                    class="banner-features stagger-item flex flex-col md:flex-row md:items-center gap-3 mb-6 md:mb-8 text-sm md:text-base text-white/90">
                                                    <div class="feature-item">
                                                        <i class="fas fa-drafting-compass text-primary mr-2"></i>
                                                        <span><?php echo htmlspecialchars($banner['feature1_text'] ?? 'Miễn phí thiết kế 3D'); ?></span>
                                                    </div>
                                                    <div class="feature-item">
                                                        <i class="fas fa-truck text-primary mr-2"></i>
                                                        <span><?php echo htmlspecialchars($banner['feature2_text'] ?? 'Giao hàng toàn quốc'); ?></span>
                                                    </div>
                                                    <div class="feature-item">
                                                        <i class="fas fa-shield-alt text-primary mr-2"></i>
                                                        <span><?php echo htmlspecialchars($banner['feature3_text'] ?? 'Bảo hành lên tới 10 năm'); ?></span>
                                                    </div>
                                                </div>

                                                <!-- Mô tả chi tiết - Thiết kế hiện đại (thêm hiệu ứng glass morphism) -->
                                                <div
                                                    class="banner-description stagger-item mb-6 md:mb-8 bg-black/20 backdrop-blur-md p-4 md:p-5 rounded-xl border border-white/10 hover:border-white/20 transition duration-300 shadow-lg">
                                                    <p class="text-sm md:text-base text-gray-100 max-w-2xl leading-relaxed">
                                                        <?php echo htmlspecialchars($banner['description_text'] ?? 'Chúng tôi chuyên thiết kế và sản xuất nội thất thông minh, hiện đại, với chất lượng cao cấp phù hợp với mọi không gian sống và làm việc.'); ?>
                                                    </p>
                                                </div>

                                                <!-- Nút CTA - Thiết kế hiện đại nổi bật hơn -->
                                                <div class="banner-buttons stagger-item flex flex-col sm:flex-row gap-3 md:gap-5 mb-6">
                                                    <?php if (!empty($banner['button_text'])): ?>
                                                        <?php
                                                        // Xác định biểu tượng cho nút CTA chính
                                                        $button_icon = get_button_icon($banner['button_text']);
                                                        ?>
                                                        <a href="<?php
                                                        if (!empty($banner['button_link'])) {
                                                            if ($banner['button_link'] == '#') {
                                                                echo '#';
                                                            } elseif (strpos($banner['button_link'], 'http://') === 0 || strpos($banner['button_link'], 'https://') === 0) {
                                                                echo $banner['button_link'];
                                                            } else {
                                                                echo BASE_URL . '/' . ltrim($banner['button_link'], '/');
                                                            }
                                                        } else {
                                                            echo '#';
                                                        }
                                                        ?>"
                                                            class="cta-button bg-primary hover:bg-primary-dark text-white px-7 py-3.5 rounded-xl inline-block transition duration-300 font-medium text-base border border-primary/50 hover:border-white/40 transform hover:-translate-y-1 hover:shadow-xl hover:shadow-primary/20 relative group overflow-hidden">
                                                            <span class="relative z-10 flex items-center justify-center">
                                                                <i
                                                                    class="fas <?php echo $button_icon; ?> mr-2 transition-transform duration-300 group-hover:scale-110"></i>
                                                                <span><?php echo htmlspecialchars($banner['button_text'] ?? 'Xem các mẫu thiết kế'); ?></span>
                                                            </span>
                                                            <span
                                                                class="absolute inset-0 bg-gradient-to-r from-primary to-primary-dark opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (!empty($banner['button2_text'])): ?>
                                                        <?php
                                                        // Xác định biểu tượng cho nút CTA phụ
                                                        $button2_icon = get_button_icon($banner['button2_text']);
                                                        ?>
                                                        <a href="<?php
                                                        if (!empty($banner['button2_link'])) {
                                                            if ($banner['button2_link'] == '#') {
                                                                echo '#';
                                                            } elseif (strpos($banner['button2_link'], 'http://') === 0 || strpos($banner['button2_link'], 'https://') === 0) {
                                                                echo $banner['button2_link'];
                                                            } else {
                                                                echo BASE_URL . '/' . ltrim($banner['button2_link'], '/');
                                                            }
                                                        } else {
                                                            echo BASE_URL . '/contact.php';
                                                        }
                                                        ?>"
                                                            class="cta-button-alt bg-white/10 hover:bg-white/20 text-white px-7 py-3.5 rounded-xl inline-block transition duration-300 font-medium text-base border border-white/30 hover:border-white/60 transform hover:-translate-y-1 hover:shadow-xl backdrop-blur-sm group relative overflow-hidden">
                                                            <span class="relative z-10 flex items-center justify-center">
                                                                <i
                                                                    class="fas <?php echo $button2_icon; ?> mr-2 transition-transform duration-300 group-hover:scale-110"></i>
                                                                <span><?php echo htmlspecialchars($banner['button2_text'] ?? 'Nhận tư vấn miễn phí'); ?></span>
                                                            </span>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Thông tin liên hệ - Thiết kế hiện đại (thêm badge kiểu mới) -->
                                                <div
                                                    class="banner-contact stagger-item contact-info flex items-center justify-center sm:justify-start flex-wrap gap-4 text-white/80 text-sm md:text-base" style="justify-content: center;"
                                                    data-responsive-center="true">
                                                    <div
                                                        class="hidden sm:flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full border border-white/10 hover:bg-white/15 transition duration-300">
                                                        <i class="fas fa-phone-alt text-primary mr-2 animate-pulse"></i>
                                                        <span
                                                            class="font-medium"><?php echo htmlspecialchars($banner['contact_phone'] ?? '************'); ?></span>
                                                    </div>
                                                    <span class="mx-2 text-white/30 hidden sm:inline-block">|</span>
                                                    <span
                                                        class="italic inline-block text-white/70 text-center sm:text-left">"<?php echo htmlspecialchars($banner['contact_slogan'] ?? 'Thiết kế chuẩn gu – Giao hàng đúng hẹn'); ?>"</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Scroll Down Indicator - Cải tiến, thêm animation -->
                                    <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 z-20 hidden md:block">
                                        <div class="scroll-down-icon">
                                            <div class="mouse">
                                                <div class="wheel"></div>
                                            </div>
                                            <div class="arrow-container">
                                                <span class="arrow-down"></span>
                                            </div>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <!-- Banner Marketing - chỉ hiển thị hình ảnh và liên kết nếu có -->
                                    <?php if (!empty($banner['link'])): ?>
                                        <a href="<?php echo $banner['link']; ?>" class="absolute inset-0 w-full h-full z-10"></a>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Custom Navigation -->
                <div class="navigation-container">
                    <!-- Nút lùi (bên trái) -->
                    <div class="swiper-button-prev-custom navigation-button navigation-button-prev">
                        <i class="fas fa-chevron-left"></i>
                    </div>

                    <!-- Nút tiến (bên phải) -->
                    <div class="swiper-button-next-custom navigation-button navigation-button-next">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <!-- Custom Pagination (Dots) cho banner -->
                <div class="banner-pagination-container"></div>
            </div>
        </div>
    <?php else: ?>
        <!-- Banner mặc định khi không có banner nào - Thiết kế hiện đại (chiều cao tối ưu) -->
        <div class="relative overflow-hidden" style="height: 75vh; width: 100%;">
            <img src="<?php echo BASE_URL; ?>/assets/images/banner.jpg" alt="Banner" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-black/80 via-black/50 to-transparent"></div>
            <div class="container mx-auto px-4 h-full relative z-10">
                <div class="flex flex-col justify-center h-full max-w-3xl">
                    <!-- Badge trên tiêu đề - Thiết kế hiện đại -->
                    <div
                        class="banner-badge inline-flex items-center bg-white/10 text-white text-sm font-medium px-5 py-2 rounded-full mb-6 backdrop-blur-md border border-white/20 shadow-glow-light hover:bg-white/15 transition-all duration-300 cursor-default relative overflow-hidden">
                        <!-- Hiệu ứng sparkle cho badge -->
                        <span class="absolute inset-0 -z-10">
                            <span class="absolute animate-ping w-1 h-1 rounded-full bg-primary/60 top-1/4 left-1/4"></span>
                            <span
                                class="absolute animate-ping delay-75 w-1.5 h-1.5 rounded-full bg-primary/60 bottom-1/4 right-1/3"></span>
                            <span
                                class="absolute animate-ping delay-150 w-1 h-1 rounded-full bg-primary/60 top-1/2 right-1/4"></span>
                        </span>
                        <i class="fas fa-award text-primary mr-2 pulse-icon"></i> Nội Thất Chất Lượng Bàng Vũ
                    </div>

                    <!-- Tiêu đề chính - Thiết kế hiện đại (hai dòng rõ ràng) -->
                    <h1 class="text-3xl md:text-5xl font-bold mb-4 md:mb-6 leading-tight banner-title">
                        <div class="text-gradient" style="white-space: nowrap;">Thiết Kế – Thi Công</div>
                        <div class="mt-1 md:mt-2">Nội Thất Theo Yêu Cầu Tại Hà Nội</div>
                    </h1>

                    <!-- Dòng mô tả phụ - Thiết kế hiện đại (một hàng trên desktop) -->
                    <div
                        class="banner-features flex flex-col md:flex-row md:justify-between items-start md:items-center gap-2 md:gap-3 mb-6 md:mb-8 text-sm md:text-base text-white/90">
                        <div class="feature-item w-full md:w-auto">
                            <i class="fas fa-drafting-compass text-primary mr-2"></i>
                            <span>Miễn phí thiết kế 3D</span>
                        </div>
                        <div class="feature-item w-full md:w-auto">
                            <i class="fas fa-truck text-primary mr-2"></i>
                            <span>Giao hàng toàn quốc</span>
                        </div>
                        <div class="feature-item w-full md:w-auto">
                            <i class="fas fa-shield-alt text-primary mr-2"></i>
                            <span>Bảo hành lên tới 10 năm</span>
                        </div>
                    </div>

                    <!-- Mô tả chi tiết - Thiết kế hiện đại (ngắn gọn hơn) -->
                    <div class="mb-6 md:mb-8 bg-black/20 backdrop-blur-sm p-3 md:p-4 rounded-xl border border-white/10">
                        <p class="text-sm md:text-base text-gray-100 max-w-2xl leading-relaxed">
                            Chúng tôi chuyên thiết kế và sản xuất nội thất thông minh, hiện đại, phù hợp với mọi không gian.
                        </p>
                    </div>

                    <!-- Nút CTA - Thiết kế hiện đại nổi bật hơn -->
                    <div class="flex flex-col sm:flex-row gap-3 md:gap-5">
                        <?php
                        // Xác định biểu tượng cho nút CTA chính
                        $button_text = 'Xem các mẫu thiết kế';
                        $button_icon = get_button_icon($button_text);
                        ?>
                        <a href="<?php echo BASE_URL; ?>/products.php"
                            class="cta-button bg-primary hover:bg-primary-dark text-white px-7 py-3.5 rounded-xl inline-block transition duration-300 font-medium text-base border border-primary/50 hover:border-white/40 transform hover:-translate-y-1 hover:shadow-xl hover:shadow-primary/20 relative group overflow-hidden">
                            <span class="relative z-10 flex items-center justify-center">
                                <i
                                    class="fas <?php echo $button_icon; ?> mr-2 transition-transform duration-300 group-hover:scale-110"></i>
                                <span><?php echo $button_text; ?></span>
                            </span>
                            <span
                                class="absolute inset-0 bg-gradient-to-r from-primary to-primary-dark opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                        </a>
                        <?php
                        // Xác định biểu tượng cho nút CTA phụ
                        $button2_text = 'Nhận tư vấn miễn phí';
                        $button2_icon = get_button_icon($button2_text);
                        ?>
                        <a href="<?php echo BASE_URL; ?>/contact.php"
                            class="cta-button-alt bg-white/10 hover:bg-white/20 text-white px-7 py-3.5 rounded-xl inline-block transition duration-300 font-medium text-base border border-white/30 hover:border-white/60 transform hover:-translate-y-1 hover:shadow-xl backdrop-blur-sm group relative overflow-hidden">
                            <span class="relative z-10 flex items-center justify-center">
                                <i
                                    class="fas <?php echo $button2_icon; ?> mr-2 transition-transform duration-300 group-hover:scale-110"></i>
                                <span><?php echo $button2_text; ?></span>
                            </span>
                        </a>
                    </div>

                    <!-- Thông tin liên hệ - Thiết kế hiện đại (nhỏ gọn hơn) -->
                    <div class="contact-info mt-4 md:mt-6 flex items-center text-white/80 text-xs md:text-sm">
                        <div class="flex items-center bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full">
                            <i class="fas fa-phone-alt text-primary mr-1.5"></i>
                            <span>************</span>
                        </div>
                        <span class="mx-2 text-white/30 hidden md:inline-block">|</span>
                        <span class="italic hidden md:inline-block">"Thiết kế chuẩn gu – Giao hàng đúng hẹn"</span>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>



<!-- Danh mục sản phẩm -->
<div class="section-spacing section-container bg-gradient-to-b from-white to-gray-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-6 text-center">
            <div class="section-title-badge fade-in-up">
                <span class="flex w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
                Khám phá
            </div>
            <h2 class="section-title fade-in-up">
                <span class="relative z-10">Danh Mục Sản Phẩm</span>
                <span class="section-title-underline"></span>
            </h2>
            <p class="section-description fade-in-up">Khám phá các danh mục sản phẩm nội thất chất lượng cao của chúng
                tôi, thiết kế để nâng tầm không gian sống của bạn</p>
        </div>

        <div class="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-5 md:gap-6 lg:gap-8 category-grid">
            <?php
            // Giới hạn hiển thị 8 danh mục trên trang chủ
            $displayed_categories = array_slice($categories, 0, 8);
            $total_categories = count($categories);

            foreach ($displayed_categories as $category):
                ?>
                <div
                    class="group h-full flex flex-col bg-white overflow-hidden rounded-xl sm:rounded-2xl shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:-translate-y-1 fade-in-up">
                    <!-- Phần hình ảnh -->
                    <div class="relative overflow-hidden aspect-video sm:aspect-[4/3] md:aspect-[3/2]">
                        <?php if ($category['image']): ?>
                            <img src="<?php echo BASE_URL; ?>/uploads/categories/<?php echo $category['image']; ?>"
                                alt="<?php echo htmlspecialchars($category['name']); ?>"
                                class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110 group-hover:saturate-[1.15]">
                        <?php else: ?>
                            <div
                                class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                <i class="fas fa-folder text-gray-400 text-3xl"></i>
                            </div>
                        <?php endif; ?>

                        <!-- Overlay gradient mượt mà -->
                        <div class="absolute inset-0 category-overlay z-5"></div>

                        <!-- Badge số lượng sản phẩm -->
                        <?php
                        // Đếm số sản phẩm trong danh mục
                        $product_count = count_products($category['id']);
                        ?>
                        <div class="absolute top-3 right-3 z-10">
                            <div
                                class="px-2 py-1 bg-white/90 backdrop-blur-sm text-primary rounded-full text-xs font-medium shadow-md border border-white/20 flex items-center">
                                <i class="fas fa-box-open text-primary/80 mr-1.5 text-[10px]"></i>
                                <span><?php echo $product_count; ?> sản phẩm</span>
                            </div>
                        </div>

                        <!-- Tên danh mục trên ảnh với nền gradient -->
                        <div class="absolute bottom-0 left-0 w-full z-10">
                            <!-- Gradient background cho text -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
                            <!-- Text container -->
                            <div class="relative p-3 sm:p-4">
                                <h3 class="text-base sm:text-lg md:text-xl font-bold text-white drop-shadow-md transition-colors duration-300">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </h3>
                            </div>
                        </div>
                    </div>

                    <!-- Phần thông tin -->
                    <div class="p-3 sm:p-4 flex flex-col flex-grow">
                        <?php if (!empty($category['description'])): ?>
                            <p class="text-gray-600 text-xs sm:text-sm mb-4 line-clamp-2 leading-relaxed">
                                <?php echo htmlspecialchars($category['description']); ?>
                            </p>
                        <?php else: ?>
                            <p class="text-gray-600 text-xs sm:text-sm mb-4 line-clamp-2 leading-relaxed">
                                Khám phá các sản phẩm <?php echo strtolower(htmlspecialchars($category['name'])); ?> cao cấp,
                                chất lượng
                            </p>
                        <?php endif; ?>

                        <!-- Nút xem chi tiết - Thiết kế sang trọng -->
                        <div class="mt-auto">
                            <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>"
                                class="luxury-category-btn">
                                <span class="btn-text">Xem chi tiết</span>
                                <span class="btn-icon-container">
                                    <i class="fas fa-arrow-right btn-icon"></i>
                                </span>
                                <span class="btn-shine"></span>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-8 fade-in-up">
            <a href="<?php echo BASE_URL; ?>/categories.php" class="inline-flex items-center justify-center px-5 sm:px-6 py-3 sm:py-3.5
                      bg-white text-gray-700 hover:text-primary
                      border border-gray-200 hover:border-primary/30
                      shadow-sm hover:shadow-lg
                      rounded-xl sm:rounded-full
                      text-sm sm:text-base font-medium
                      transition-all duration-300 transform hover:-translate-y-1 group">
                <span>Xem tất cả danh mục</span>
                <span
                    class="ml-2 w-6 h-6 rounded-full bg-gray-100 group-hover:bg-primary/10 flex items-center justify-center transition-colors duration-300">
                    <i
                        class="fas fa-arrow-right text-xs transition-transform duration-300 group-hover:translate-x-0.5"></i>
                </span>
            </a>
        </div>
    </div>
</div>

<!-- Sản phẩm nổi bật -->
<div class="section-spacing section-container bg-gradient-to-b from-gray-50 to-white">
    <div class="container mx-auto px-4">
        <div class="mb-6 text-center">
            <div class="section-title-badge fade-in-up">
                <span class="flex w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
                Khuyên dùng
            </div>
            <h2 class="section-title fade-in-up">
                <span class="relative z-10">Sản Phẩm Nổi Bật</span>
                <span class="section-title-underline"></span>
            </h2>
            <p class="section-description fade-in-up">Những sản phẩm nổi bật nhất của chúng tôi, được lựa chọn kỹ lưỡng
                về chất lượng và thiết kế</p>
        </div>

        <!-- Sản phẩm nổi bật Slider -->
        <div class="featured-products-slider fade-in-up">
            <div class="swiper featured-swiper">
                <div class="swiper-wrapper">
                    <?php foreach ($featured_products as $product): ?>
                        <div class="swiper-slide">
                            <div
                                class="group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200 hover:-translate-y-2 fade-in-up">
                                <!-- Ảnh sản phẩm với tỉ lệ 1:1 -->
                                <div class="relative overflow-hidden aspect-square">
                                    <a href="<?php echo get_product_url($product['slug']); ?>"
                                        class="block w-full h-full">
                                        <?php if (!empty($product['image'])): ?>
                                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>"
                                                alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gray-300 flex items-center justify-center">
                                                <i class="fas fa-image text-gray-500 text-4xl"></i>
                                            </div>
                                        <?php endif; ?>
                                    </a>

                                    <!-- Premium Sale Badge -->
                                    <?php if (isset($product['sale_price']) && $product['sale_price'] > 0 && $product['price'] > $product['sale_price']): ?>
                                        <?php $discount_percent = round(($product['price'] - $product['sale_price']) / $product['price'] * 100); ?>
                                        <div class="premium-sale-badge">
                                            <div class="badge-content">
                                                <span class="discount-percent">-<?php echo $discount_percent; ?>%</span>
                                                <span class="sale-text">SALE</span>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="product-info-wrapper flex flex-col flex-grow">
                                    <!-- Product Title - Max 2 lines -->
                                    <div class="product-title mb-3">
                                        <a href="<?php echo get_product_url($product['slug']); ?>"
                                            class="block">
                                            <h3
                                                class="text-lg font-semibold text-gray-800 hover:text-blue-500 transition duration-200 line-clamp-2 leading-tight">
                                                <?php echo htmlspecialchars($product['name']); ?>
                                            </h3>
                                        </a>
                                    </div>

                                    <!-- Premium Price Section -->
                                    <div class="premium-price-section">
                                        <?php if (isset($product['price_type']) && $product['price_type'] === 'contact'): ?>
                                            <!-- Liên hệ báo giá - Giống products.php -->
                                            <div class="contact-price-container">
                                                <div class="contact-price-main">
                                                    GỌI NGAY
                                                </div>
                                                <div class="contact-price-subtitle">
                                                    Liên hệ báo giá
                                                </div>
                                            </div>
                                        <?php elseif (isset($product['sale_price']) && $product['sale_price'] > 0): ?>
                                            <!-- Sản phẩm có giá sale -->
                                            <div class="price-container">
                                                <div class="original-price"><?php echo format_currency($product['price']); ?>
                                                </div>
                                                <div class="sale-price"><?php echo format_currency($product['sale_price']); ?>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <!-- Sản phẩm giá thường -->
                                            <div class="regular-price-container">
                                                <div class="price-label">Giá bán</div>
                                                <div class="main-price"><?php echo format_currency($product['price']); ?></div>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Rating and Sales Section -->
                                    <div class="product-rating-sales">
                                        <div class="rating-section">
                                            <div class="stars">
                                                <?php
                                                $avg_rating = isset($product['avg_rating']) ? $product['avg_rating'] : 5;
                                                $rating_stars = round($avg_rating);
                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= $rating_stars) {
                                                        echo '<i class="fas fa-star"></i>';
                                                    } else {
                                                        echo '<i class="far fa-star"></i>';
                                                    }
                                                }
                                                ?>
                                            </div>
                                            <span class="rating-text"><?php echo number_format($avg_rating, 1); ?></span>
                                        </div>
                                        <div class="sales-section">
                                            <i class="fas fa-shopping-cart"></i>
                                            <span><?php echo isset($product['sold']) ? $product['sold'] : 0; ?> đã
                                                bán</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Điều hướng slider -->
                <div class="flex justify-center items-center space-x-6 mt-6">
                    <div
                        class="featured-button-prev w-12 h-12 flex items-center justify-center bg-white rounded-full shadow-md hover:shadow-lg cursor-pointer transition-all duration-300 hover:bg-blue-500 hover:text-white text-blue-500 hover:-translate-y-1">
                        <i class="fas fa-chevron-left text-lg"></i>
                    </div>
                    <div
                        class="featured-button-next w-12 h-12 flex items-center justify-center bg-white rounded-full shadow-md hover:shadow-lg cursor-pointer transition-all duration-300 hover:bg-blue-500 hover:text-white text-blue-500 hover:-translate-y-1">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </div>
                </div>

                <!-- Phân trang dots -->
                <div class="featured-slider-pagination flex justify-center items-center space-x-2 mt-4"></div>
            </div>
        </div>

        <div class="text-center mt-8 fade-in-up">
            <a href="<?php echo BASE_URL; ?>/products.php" class="inline-flex items-center justify-center px-5 sm:px-6 py-3 sm:py-3.5
                      bg-white text-gray-700 hover:text-primary
                      border border-gray-200 hover:border-primary/30
                      shadow-sm hover:shadow-lg
                      rounded-xl sm:rounded-full
                      text-sm sm:text-base font-medium
                      transition-all duration-300 transform hover:-translate-y-1 group">
                <span>Xem tất cả sản phẩm</span>
                <span
                    class="ml-2 w-6 h-6 rounded-full bg-gray-100 group-hover:bg-primary/10 flex items-center justify-center transition-colors duration-300">
                    <i
                        class="fas fa-arrow-right text-xs transition-transform duration-300 group-hover:translate-x-0.5"></i>
                </span>
            </a>
        </div>
    </div>
</div>



<!-- Dịch vụ thiết kế - Optimized -->
<div class="design-services-section section-container">
    <!-- Background decorations - Simplified -->
    <div class="design-services-bg">
        <div class="bg-shape shape-1"></div>
        <div class="bg-shape shape-2"></div>
        <div class="bg-shape shape-3"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Section title -->
        <div class="design-services-title-container fade-in-up">
            <div class="design-services-badge">
                <span class="pulse-dot"></span>
                Dịch vụ chuyên nghiệp
            </div>
            <h2 class="design-services-heading">
                Dịch Vụ Thiết Kế
                <span class="design-services-heading-underline"></span>
            </h2>
            <p class="design-services-description mt-4">
                Nâng tầm không gian sống với dịch vụ thiết kế nội thất chuyên nghiệp, sáng tạo và cá nhân hoá theo phong
                cách riêng của bạn
            </p>
        </div>

        <!-- Service cards -->
        <div class="design-services-cards">
            <!-- Free Interior Design Service -->
            <div class="design-service-card fade-in-up">
                <div class="design-service-image">
                    <img src="https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                        alt="Thiết kế nội thất miễn phí">
                    <div class="design-service-overlay"></div>
                </div>
                <div class="design-service-content">
                    <h3 class="design-service-title">Thiết Kế Nội Thất Miễn Phí</h3>
                    <p class="design-service-description">
                        Dịch vụ tư vấn và thiết kế 3D miễn phí, giúp bạn hình dung rõ ràng không gian sống trước khi
                        quyết định. Đội ngũ thiết kế chuyên nghiệp sẽ lắng nghe và đưa ra giải pháp tối ưu cho không
                        gian của bạn.
                    </p>
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="design-service-button primary">
                        Đặt lịch ngay
                        <span class="icon ml-2">
                            <i class="fas fa-calendar-check"></i>
                        </span>
                    </a>
                </div>
            </div>

            <!-- Personalized Design Process -->
            <div class="design-service-card fade-in-up">
                <div class="design-service-image">
                    <img src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                        alt="Quy trình thiết kế cá nhân hoá">
                    <div class="design-service-overlay"></div>
                </div>
                <div class="design-service-content">
                    <h3 class="design-service-title">Quy Trình Thiết Kế Cá Nhân Hoá</h3>
                    <p class="design-service-description">
                        Chúng tôi hiểu rằng mỗi không gian đều độc đáo như chính chủ nhân của nó. Quy trình thiết kế của
                        chúng tôi được cá nhân hoá hoàn toàn, từ khảo sát đến hoàn thiện, đảm bảo kết quả cuối cùng phản
                        ánh đúng phong cách và nhu cầu của bạn.
                    </p>
                    <a href="<?php echo BASE_URL; ?>/design-process.php" class="design-service-button secondary group">
                        Xem chi tiết
                        <span class="icon ml-2 group-hover:translate-x-1 transition-transform duration-300">
                            <i class="fas fa-arrow-right"></i>
                        </span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Quality Badges -->
        <div class="quality-badges fade-in-up">
            <div class="quality-badge">
                <div class="quality-badge-icon">
                    <i class="fas fa-medal" style="color: #F37321;"></i>
                </div>
                <div class="quality-badge-text">Chất lượng đảm bảo</div>
            </div>
            <div class="quality-badge">
                <div class="quality-badge-icon">
                    <i class="fas fa-drafting-compass" style="color: #F37321;"></i>
                </div>
                <div class="quality-badge-text">Thiết kế chuyên nghiệp</div>
            </div>
            <div class="quality-badge">
                <div class="quality-badge-icon">
                    <i class="fas fa-clock" style="color: #F37321;"></i>
                </div>
                <div class="quality-badge-text">Đúng tiến độ</div>
            </div>
            <div class="quality-badge">
                <div class="quality-badge-icon">
                    <i class="fas fa-headset" style="color: #F37321;"></i>
                </div>
                <div class="quality-badge-text">Hỗ trợ 24/7</div>
            </div>
        </div>
    </div>
</div>

<!-- Debug info (sẽ xóa sau khi fix xong) -->
<div class="hidden">
    <pre id="debug-info">
        Số sản phẩm nổi bật: <?php echo count($featured_products); ?>
        Dữ liệu: <?php echo print_r($featured_products, true); ?>
    </pre>
</div>

<!-- Sản phẩm nội thất bàng vũ - Thiết kế mới -->
<div
    class="section-spacing section-container bg-gradient-to-b from-white via-gray-50 to-white relative overflow-hidden">
    <!-- Các hình trang trí nền -->
    <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div class="absolute top-10 left-10 w-64 h-64 bg-primary/5 rounded-full filter blur-3xl"></div>
        <div class="absolute bottom-10 right-10 w-80 h-80 bg-blue-500/5 rounded-full filter blur-3xl"></div>
        <div class="absolute top-1/3 right-1/4 w-40 h-40 bg-yellow-500/5 rounded-full filter blur-3xl"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Tiêu đề chính với thiết kế hiện đại và tinh tế -->
        <div class="text-center mb-8 relative main-title-container">
            <!-- Hiệu ứng nền trang trí -->
            <div class="absolute inset-0 -z-10 overflow-hidden">
                <div
                    class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[120%] h-40 bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 blur-3xl opacity-70 transform -rotate-1">
                </div>
            </div>

            <!-- Badge trên tiêu đề -->
            <div class="section-title-badge animate-fade-in-up" style="animation-delay: 0.1s;">
                <span class="flex w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
                Bộ sưu tập đặc biệt
            </div>

            <!-- Tiêu đề chính -->
            <h2 class="section-title animate-fade-in-up" style="animation-delay: 0.2s;">
                <span class="relative z-10">Sản Phẩm Nội Thất Bàng Vũ</span>
                <span class="section-title-underline"></span>
            </h2>

            <!-- Mô tả -->
            <p class="section-description animate-fade-in-up" style="animation-delay: 0.3s;">Khám phá bộ sưu tập nội
                thất cao cấp với thiết kế tinh tế, chất lượng vượt trội và giá cả hợp lý</p>
        </div>

        <?php if (count($homepage_categories) > 0): ?>
            <!-- Danh mục sản phẩm hiển thị lần lượt -->
            <div class="space-y-12">
                <?php foreach ($homepage_categories as $category): ?>
                    <?php
                    // Sử dụng function mới để lấy sản phẩm từ danh mục và tất cả danh mục con
                    // Nếu là danh mục cha (parent_id = null), sẽ lấy sản phẩm từ tất cả danh mục con
                    // Nếu là danh mục con, chỉ lấy sản phẩm từ chính danh mục đó
                    if (empty($category['parent_id'])) {
                        // Đây là danh mục cha - lấy sản phẩm từ tất cả danh mục con
                        $category_products = get_products_from_category_and_children($category['id'], 8, 0, 1);
                    } else {
                        // Đây là danh mục con - chỉ lấy sản phẩm từ chính danh mục này
                        $category_products = get_products(8, 0, $category['id'], null, null, 1);

                        // Lọc chỉ lấy các sản phẩm có show_on_homepage = 1
                        $category_products = array_filter($category_products, function ($product) {
                            return isset($product['show_on_homepage']) && $product['show_on_homepage'] == 1;
                        });
                    }

                    // Chỉ hiển thị nếu có sản phẩm
                    if (count($category_products) > 0):

                        // Include file mega-menu.php nếu chưa được include
                        if (!function_exists('get_category_icon')) {
                            require_once __DIR__ . '/includes/mega-menu.php';
                        }
                        // Lấy icon phù hợp cho danh mục
                        $icon_class = get_category_icon($category['name']);
                        ?>
                        <div class="category-section fade-in-up" data-aos="fade-up">
                            <!-- Header danh mục với thiết kế hiện đại và tinh tế -->
                            <div
                                class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 category-header-container">
                                <!-- Phần tiêu đề danh mục -->
                                <div class="flex items-center mb-4 sm:mb-0 group">
                                    <!-- Icon danh mục với hiệu ứng -->
                                    <div
                                        class="category-icon mr-4 bg-gradient-to-br from-primary/10 to-primary/20 p-3 sm:p-4 rounded-xl shadow-sm transform transition-all duration-300 group-hover:shadow-md group-hover:scale-105">
                                        <i class="fas <?php echo $icon_class; ?> text-primary text-lg sm:text-xl"></i>
                                        <div class="category-icon-glow"></div>
                                    </div>

                                    <!-- Tên danh mục và số lượng sản phẩm -->
                                    <div>
                                        <h3
                                            class="text-xl sm:text-2xl font-bold text-gray-800 flex items-center flex-wrap category-title">
                                            <span class="category-name"><?php echo htmlspecialchars($category['name']); ?></span>
                                            <span
                                                class="ml-0 mt-1 sm:ml-3 sm:mt-0 text-xs font-normal bg-gradient-to-r from-primary/10 to-primary/20 text-primary px-2.5 py-1 rounded-full shadow-sm border border-primary/5 product-count">
                                                <?php echo count($category_products); ?> sản phẩm
                                            </span>
                                        </h3>
                                        <!-- Gạch chân trang trí -->
                                        <div
                                            class="category-title-decoration mt-1 h-0.5 w-16 bg-gradient-to-r from-transparent via-primary/30 to-transparent rounded-full">
                                        </div>
                                    </div>
                                </div>

                                <!-- Nút xem tất cả với thiết kế mới -->
                                <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>"
                                    class="view-all-btn group flex items-center bg-white px-5 py-2.5 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-300 shadow-sm hover:shadow transform hover:-translate-y-0.5 w-full sm:w-auto justify-center sm:justify-start">
                                    <span
                                        class="text-gray-700 group-hover:text-primary font-medium transition-colors duration-300">Xem
                                        tất cả</span>
                                    <div
                                        class="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center ml-2 group-hover:bg-primary/20 transition-colors duration-300 view-all-icon">
                                        <i
                                            class="fas fa-arrow-right text-xs text-primary transform group-hover:translate-x-0.5 transition-transform duration-300"></i>
                                    </div>
                                </a>
                            </div>

                            <!-- Grid sản phẩm với thiết kế giống sản phẩm nổi bật -->
                            <div class="category-products-slider">
                                <div class="swiper-container category-swiper-<?php echo $category['id']; ?> category-swiper-container"
                                    data-category-id="<?php echo $category['id']; ?>">
                                    <div class="swiper-wrapper">
                                        <?php foreach ($category_products as $product): ?>
                                            <div class="swiper-slide">
                                                <div
                                                    class="group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200 hover:-translate-y-2 fade-in-up">
                                                    <!-- Ảnh sản phẩm với tỉ lệ 1:1 -->
                                                    <div class="relative overflow-hidden aspect-square">
                                                        <a href="<?php echo get_product_url($product['slug']); ?>"
                                                            class="block w-full h-full">
                                                            <?php if (!empty($product['image'])): ?>
                                                                <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>"
                                                                    alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                                    class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                                            <?php else: ?>
                                                                <div class="w-full h-full bg-gray-300 flex items-center justify-center">
                                                                    <i class="fas fa-image text-gray-500 text-4xl"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                        </a>

                                                        <!-- Premium Sale Badge -->
                                                        <?php if (isset($product['sale_price']) && $product['sale_price'] > 0 && $product['price'] > $product['sale_price']): ?>
                                                            <?php $discount_percent = round(($product['price'] - $product['sale_price']) / $product['price'] * 100); ?>
                                                            <div class="premium-sale-badge">
                                                                <div class="badge-content">
                                                                    <span class="discount-percent">-<?php echo $discount_percent; ?>%</span>
                                                                    <span class="sale-text">SALE</span>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="product-info-wrapper flex flex-col flex-grow">
                                                        <!-- Product Title - Max 2 lines -->
                                                        <div class="product-title mb-3">
                                                            <a href="<?php echo get_product_url($product['slug']); ?>"
                                                                class="block">
                                                                <h3
                                                                    class="text-lg font-semibold text-gray-800 hover:text-blue-500 transition duration-200 line-clamp-2 leading-tight">
                                                                    <?php echo htmlspecialchars($product['name']); ?>
                                                                </h3>
                                                            </a>
                                                        </div>

                                                        <!-- Premium Price Section -->
                                                        <div class="premium-price-section">
                                                            <?php if (isset($product['price_type']) && $product['price_type'] === 'contact'): ?>
                                                                <!-- Liên hệ báo giá - Giống products.php -->
                                                                <div class="contact-price-container">
                                                                    <div class="contact-price-main">
                                                                        GỌI NGAY
                                                                    </div>
                                                                    <div class="contact-price-subtitle">
                                                                        Liên hệ báo giá
                                                                    </div>
                                                                </div>
                                                            <?php elseif (isset($product['sale_price']) && $product['sale_price'] > 0): ?>
                                                                <!-- Sản phẩm có giá sale -->
                                                                <div class="price-container">
                                                                    <div class="original-price">
                                                                        <?php echo format_currency($product['price']); ?></div>
                                                                    <div class="sale-price">
                                                                        <?php echo format_currency($product['sale_price']); ?></div>
                                                                </div>
                                                            <?php else: ?>
                                                                <!-- Sản phẩm giá thường -->
                                                                <div class="regular-price-container">
                                                                    <div class="price-label">Giá bán</div>
                                                                    <div class="main-price">
                                                                        <?php echo format_currency($product['price']); ?></div>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>

                                                        <!-- Rating and Sales Section -->
                                                        <div class="product-rating-sales">
                                                            <div class="rating-section">
                                                                <div class="stars">
                                                                    <?php
                                                                    $avg_rating = isset($product['avg_rating']) ? $product['avg_rating'] : 5;
                                                                    $rating_stars = round($avg_rating);
                                                                    for ($i = 1; $i <= 5; $i++) {
                                                                        if ($i <= $rating_stars) {
                                                                            echo '<i class="fas fa-star"></i>';
                                                                        } else {
                                                                            echo '<i class="far fa-star"></i>';
                                                                        }
                                                                    }
                                                                    ?>
                                                                </div>
                                                                <span
                                                                    class="rating-text"><?php echo number_format($avg_rating, 1); ?></span>
                                                            </div>
                                                            <div class="sales-section">
                                                                <i class="fas fa-shopping-cart"></i>
                                                                <span><?php echo isset($product['sold']) ? $product['sold'] : 0; ?> đã
                                                                    bán</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <!-- Điều hướng slider -->
                                    <div class="flex justify-center items-center space-x-6 mt-6">
                                        <div
                                            class="category-button-prev-<?php echo $category['id']; ?> w-12 h-12 flex items-center justify-center bg-white rounded-full shadow-md hover:shadow-lg cursor-pointer transition-all duration-300 hover:bg-blue-500 hover:text-white text-blue-500 hover:-translate-y-1">
                                            <i class="fas fa-chevron-left text-lg"></i>
                                        </div>
                                        <div
                                            class="category-button-next-<?php echo $category['id']; ?> w-12 h-12 flex items-center justify-center bg-white rounded-full shadow-md hover:shadow-lg cursor-pointer transition-all duration-300 hover:bg-blue-500 hover:text-white text-blue-500 hover:-translate-y-1">
                                            <i class="fas fa-chevron-right text-lg"></i>
                                        </div>
                                    </div>

                                    <!-- Phân trang dots -->
                                    <div
                                        class="category-slider-pagination-<?php echo $category['id']; ?> flex justify-center items-center space-x-2 mt-4">
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- Thông báo khi chưa có danh mục -->
            <div class="text-center py-16 bg-white rounded-xl shadow-sm">
                <div class="text-gray-400 mb-4">
                    <i class="fas fa-box-open text-5xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">Chưa có danh mục nào được hiển thị</h3>
                <p class="text-gray-500">Vui lòng vào trang quản trị để cấu hình danh mục hiển thị ở trang chủ.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Giới thiệu công ty và năng lực cạnh tranh -->
<div class="section-container">
    <div class="company-intro-section">
        <!-- Background pattern -->
        <div class="company-intro-bg"></div>

        <div class="container mx-auto">
            <div class="company-intro-container">
                <!-- Left content - Company info -->
                <div class="company-info">
                    <div class="company-capabilities-title">
                        <div class="company-intro-badge fade-in-up" style="animation-delay: 0.1s;">
                            <span class="badge-icon"><i class="fas fa-star"></i></span>
                            Năng lực cạnh tranh
                        </div>

                        <h2 class="company-intro-title fade-in-up" style="animation-delay: 0.2s;">
                            Nội Thất <span class="company-intro-title-highlight">Bàng Vũ</span>
                        </h2>
                    </div>

                    <div class="competitive-advantages fade-in-up" style="animation-delay: 0.3s;">
                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-industry"></i>
                            </div>
                            <div class="advantage-content">
                                <h4>Xưởng sản xuất trực tiếp</h4>
                                <p>Tiết kiệm ngân sách cho khách hàng, không qua trung gian</p>
                            </div>
                        </div>

                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div class="advantage-content">
                                <h4>Giám đốc đi từ thợ chính</h4>
                                <p>Trực tiếp làm việc với khách hàng, hiểu rõ nhu cầu</p>
                            </div>
                        </div>

                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-drafting-compass"></i>
                            </div>
                            <div class="advantage-content">
                                <h4>Đội ngũ chuyên nghiệp</h4>
                                <p>Thiết kế - Kỹ thuật trên 15 năm kinh nghiệm</p>
                            </div>
                        </div>

                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-cubes"></i>
                            </div>
                            <div class="advantage-content">
                                <h4>Tư vấn - Khảo sát miễn phí</h4>
                                <p>Thiết kế 3D miễn phí, tư vấn tận tâm</p>
                            </div>
                        </div>

                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="advantage-content">
                                <h4>Cam kết thi công</h4>
                                <p>Đúng tiến độ, đúng chất liệu như trên hợp đồng</p>
                            </div>
                        </div>

                        <div class="advantage-item">
                            <div class="advantage-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="advantage-content">
                                <h4>Bảo hành - Bảo trì</h4>
                                <p>Bảo hành 10 năm - Bảo trì trọn đời</p>
                            </div>
                        </div>
                    </div>

                    <div class="materials-section fade-in-up" style="animation-delay: 0.4s;">
                        <div class="materials-title">
                            <i class="fas fa-certificate"></i> Chất liệu cao cấp
                        </div>

                        <div class="materials-list">
                            <div class="material-tag">
                                <i class="fas fa-circle"></i> An Cường
                            </div>
                            <div class="material-tag">
                                <i class="fas fa-circle"></i> Mộc Phát
                            </div>
                            <div class="material-tag">
                                <i class="fas fa-circle"></i> Ba Thanh
                            </div>
                            <div class="material-tag">
                                <i class="fas fa-circle"></i> Ecoplast
                            </div>
                            <div class="material-tag">
                                <i class="fas fa-circle"></i> Chinhuei+
                            </div>
                            <div class="material-tag">
                                <i class="fas fa-circle"></i> Song Long
                            </div>
                        </div>
                    </div>

                    <!-- Mobile-optimized call button -->
                    <a href="tel:0972774646" class="call-button fade-in-up" data-phone="0972774646"
                        style="animation-delay: 0.5s;">
                        <span class="call-icon"><i class="fas fa-phone-alt"></i></span>
                        <span class="hidden sm:inline">Gọi ngay:</span>
                        <span>************</span>
                    </a>
                </div>

                <!-- Right content - Image -->
                <div class="company-image-container fade-in-up" style="animation-delay: 0.6s;">
                    <div class="company-image-wrapper">
                        <img src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                            alt="Nội Thất Bàng Vũ" class="company-image">
                        <div class="image-overlay"></div>

                        <!-- Company stats -->
                        <div class="company-stats">
                            <div class="stat-item">
                                <div class="stat-value" data-value="15">15</div>
                                <div class="stat-label">Năm<span class="hidden xs:inline"> kinh nghiệm</span></div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" data-value="500">500<span class="hidden xs:inline">+</span>
                                </div>
                                <div class="stat-label">Dự án<span class="hidden xs:inline"> hoàn thành</span></div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" data-value="100">100%</div>
                                <div class="stat-label"><span class="hidden xs:inline">Khách hàng </span>Hài lòng</div>
                            </div>
                        </div>
                    </div>

                    <!-- Warranty badge -->
                    <div class="warranty-badge">
                        <div class="icon">
                            <i class="fas fa-award"></i>
                        </div>
                        <div class="content">
                            <div class="years" data-years="10">10</div>
                            <div class="text">Năm<span class="hidden xs:inline"> bảo hành</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Các Đối Tác Chính Thức -->
<div class="section-container">
    <div class="official-partners-section">
        <!-- Background pattern -->
        <div class="official-partners-bg"></div>

        <div class="container mx-auto">
            <div class="official-partners-container">
                <!-- Section title -->
                <div class="official-partners-title-container">
                    <div class="official-partners-badge fade-in-up">
                        <span class="badge-icon"><i class="fas fa-handshake"></i></span>
                        Đối tác tin cậy
                    </div>

                    <h2 class="official-partners-heading fade-in-up">
                        Các Đối Tác Chính Thức
                    </h2>

                    <p class="official-partners-description fade-in-up">
                        Nội Thất Bàng Vũ tự hào hợp tác với các thương hiệu hàng đầu để mang đến sản phẩm chất lượng cao
                        nhất cho khách hàng.
                    </p>
                </div>

                <!-- Đối tác về gỗ -->
                <div class="partner-category fade-in-up">
                    <div class="flex justify-center mb-6">
                        <h3 class="partner-category-title">
                            <i class="fas fa-tree text-blue-500 mr-2"></i> Đối Tác Vật Liệu Gỗ
                        </h3>
                    </div>

                    <div class="partners-grid">
                        <!-- An Cường -->
                        <div class="partner-item">
                            <span class="partner-type wood">Gỗ công nghiệp</span>
                            <div class="partner-logo-container">
                                <img src="<?php echo BASE_URL; ?>/assets/images/logo-doi-tac/logo-an-cuong.jpg"
                                    alt="An Cường" class="partner-logo">
                            </div>
                            <div class="partner-info">
                                <p class="partner-description">Nhà cung cấp gỗ công nghiệp hàng đầu Việt Nam với chất
                                    lượng đạt chuẩn quốc tế.</p>
                            </div>
                        </div>

                        <!-- Ba Thanh -->
                        <div class="partner-item">
                            <span class="partner-type wood">Gỗ công nghiệp</span>
                            <div class="partner-logo-container">
                                <img src="<?php echo BASE_URL; ?>/assets/images/logo-doi-tac/logo_ba_thanh.png"
                                    alt="Ba Thanh" class="partner-logo">
                            </div>
                            <div class="partner-info">
                                <p class="partner-description">Chuyên cung cấp các loại gỗ công nghiệp chất lượng cao,
                                    đa dạng mẫu mã và màu sắc.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Đối tác về nhựa -->
                <div class="partner-category fade-in-up">
                    <div class="flex justify-center mb-6">
                        <h3 class="partner-category-title">
                            <i class="fas fa-recycle text-green-500 mr-2"></i> Đối Tác Vật Liệu Nhựa
                        </h3>
                    </div>

                    <div class="partners-grid">
                        <!-- Chinhuei 2.0 Plus -->
                        <div class="partner-item">
                            <span class="partner-type plastic">Nhựa cao cấp</span>
                            <div class="partner-logo-container">
                                <img src="<?php echo BASE_URL; ?>/assets/images/logo-doi-tac/logo-ChinHuei.png"
                                    alt="Chinhuei 2.0 Plus" class="partner-logo">
                            </div>
                            <div class="partner-info">
                                <p class="partner-description">Vật liệu nhựa cao cấp với độ bền cao, chống trầy xước và
                                    dễ dàng vệ sinh.</p>
                            </div>
                        </div>

                        <!-- Ecoplast -->
                        <div class="partner-item">
                            <span class="partner-type plastic">Nhựa sinh thái</span>
                            <div class="partner-logo-container">
                                <img src="<?php echo BASE_URL; ?>/assets/images/logo-doi-tac/logo-ecoplast.jpg"
                                    alt="Ecoplast" class="partner-logo">
                            </div>
                            <div class="partner-info">
                                <p class="partner-description">Vật liệu nhựa thân thiện với môi trường, an toàn cho sức
                                    khỏe và có tính thẩm mỹ cao.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Thêm CSS cho thiết kế mới -->
<style>
    /* Biến CSS cho màu sắc */
    :root {
        --color-primary: #3B82F6;
        /* Màu primary (blue-500) */
        --color-primary-dark: #2563EB;
        /* Màu primary-dark (blue-600) */
        --color-primary-rgb: 59, 130, 246;
        /* RGB của màu primary */
        --color-secondary: #F59E0B;
        /* Màu secondary (amber-500) */
        --color-secondary-rgb: 245, 158, 11;
        /* RGB của màu secondary */
        --color-accent: #10B981;
        /* Màu accent (emerald-500) */
        --color-accent-rgb: 16, 185, 129;
        /* RGB của màu accent */
        --color-danger: #EF4444;
        /* Màu danger (red-500) */
        --color-danger-rgb: 239, 68, 68;
        /* RGB của màu danger */
        --color-success: #10B981;
        /* Màu success (emerald-500) */
        --color-success-rgb: 16, 185, 129;
        /* RGB của màu success */
        --color-warning: #F59E0B;
        /* Màu warning (amber-500) */
        --color-warning-rgb: 245, 158, 11;
        /* RGB của màu warning */
        --color-info: #3B82F6;
        /* Màu info (blue-500) */
        --color-info-rgb: 59, 130, 246;
        /* RGB của màu info */
        --color-light: #F3F4F6;
        /* Màu light (gray-100) */
        --color-light-rgb: 243, 244, 246;
        /* RGB của màu light */
        --color-dark: #1F2937;
        /* Màu dark (gray-800) */
        --color-dark-rgb: 31, 41, 55;
        /* RGB của màu dark */
        --color-white: #FFFFFF;
        /* Màu white */
        --color-white-rgb: 255, 255, 255;
        /* RGB của màu white */
        --color-black: #000000;
        /* Màu black */
        --color-black-rgb: 0, 0, 0;
        /* RGB của màu black */

        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
        --shadow-outline: 0 0 0 3px rgba(66, 153, 225, 0.5);
        --shadow-none: none;

        --radius-none: 0;
        --radius-sm: 0.125rem;
        --radius-md: 0.375rem;
        --radius-lg: 0.5rem;
        --radius-xl: 0.75rem;
        --radius-2xl: 1rem;
        --radius-3xl: 1.5rem;
        --radius-full: 9999px;
    }

    /* Hiệu ứng fade-in cho các phần tử - loại trừ handbook-card */
    .fade-in-up:not(.handbook-card) {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.6s ease, transform 0.6s ease;
    }

    .fade-in-up.visible:not(.handbook-card) {
        opacity: 1;
        transform: translateY(0);
    }

    /* Hiệu ứng fade-in chỉ khi scroll - cho thẻ sản phẩm */
    .scroll-fade-in-up {
        opacity: 1;
        transform: translateY(0);
        transition: opacity 0.6s ease, transform 0.6s ease;
    }

    /* Trạng thái ẩn ban đầu cho scroll fade */
    .scroll-fade-in-up.scroll-hidden {
        opacity: 0;
        transform: translateY(20px);
    }

    /* Trạng thái hiển thị khi scroll */
    .scroll-fade-in-up.scroll-visible {
        opacity: 1;
        transform: translateY(0);
    }

    /* Hiệu ứng animation cho tiêu đề */
    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-up {
        animation: fade-in-up 0.8s ease forwards;
    }

    /* Các class CSS đã được thay thế bằng Tailwind */

    /* CSS cho mobile đã được thay thế bằng Tailwind */
    @media (max-width: 767px) {

        /* Giữ lại một số CSS cần thiết cho mobile */
        .main-title-container {
            padding: 1rem 0;
        }

        .category-header-container {
            margin-bottom: 1rem;
            padding: 0.5rem 0;
        }

        .category-icon {
            padding: 0.5rem;
            margin-right: 0.75rem;
        }

        .category-icon i {
            font-size: 1rem;
        }

        .category-title {
            font-size: 1.25rem;
            line-height: 1.3;
            flex-wrap: wrap;
        }

        .category-name {
            margin-bottom: 0.25rem;
            width: 100%;
        }

        .product-count {
            margin-left: 0;
            margin-top: 0.25rem;
            font-size: 0.7rem;
            padding: 0.25rem 0.75rem;
        }

        .category-title-decoration {
            width: 2rem;
            margin-top: 0.25rem;
        }

        /* Cải thiện nút xem tất cả trên mobile */
        .view-all-btn {
            width: 100%;
            justify-content: center;
            padding: 0.5rem 1rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }

        .view-all-icon {
            width: 1.25rem;
            height: 1.25rem;
        }
    }

    /* Hiệu ứng hover cho nút xem tất cả */
    .group:hover .group-hover\:translate-x-1 {
        transform: translateX(4px);
    }

    /* Thiết kế tiêu đề chính */
    .main-title-container {
        position: relative;
        padding: 2rem 0;
    }

    .main-title-text {
        position: relative;
        display: inline-block;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .main-title-container:hover .main-title-text {
        transform: translateY(-2px);
        text-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .main-title-underline {
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .main-title-container:hover .main-title-underline {
        transform: scaleX(1) translateY(2px);
        opacity: 0.9;
    }

    /* Thiết kế tiêu đề danh mục */
    .category-header-container {
        position: relative;
        overflow: hidden;
        padding: 0.5rem 0;
        border-radius: 0.75rem;
    }

    .category-header-container::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(to right, transparent, rgba(209, 213, 219, 0.5), transparent);
        z-index: -1;
        opacity: 0.7;
    }

    .category-icon {
        position: relative;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        overflow: hidden;
    }

    .category-icon-glow {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at center, rgba(243, 115, 33, 0.3), transparent 70%);
        border-radius: inherit;
        opacity: 0;
        transition: opacity 0.4s ease, transform 0.4s ease;
        transform: scale(0.8);
        z-index: -1;
    }

    .category-icon:hover .category-icon-glow,
    .category-section:hover .category-icon .category-icon-glow {
        opacity: 1;
        transform: scale(1.2);
    }

    .category-title {
        position: relative;
        transition: all 0.3s ease;
    }

    .category-name {
        position: relative;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .category-section:hover .category-name {
        color: var(--primary, #F37321);
        transform: translateX(2px);
    }

    .category-title-decoration {
        transition: all 0.4s ease;
        transform-origin: left center;
    }

    .category-section:hover .category-title-decoration {
        width: 100%;
        opacity: 0.8;
    }

    .product-count {
        transition: all 0.3s ease;
    }

    .category-section:hover .product-count {
        background: linear-gradient(to right, rgba(243, 115, 33, 0.2), rgba(243, 115, 33, 0.3));
        color: #d15a0a;
    }

    .view-all-btn {
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .view-all-icon {
        transition: all 0.3s ease;
    }

    .view-all-btn:hover .view-all-icon {
        transform: scale(1.1);
    }

    /* Cải thiện hiệu ứng cho thẻ sản phẩm */
    .product-card {
        position: relative;
        z-index: 1;
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .product-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        opacity: 0;
        border-radius: inherit;
        transition: opacity 0.3s ease;
        z-index: -1;
    }

    .product-card:hover::after {
        opacity: 1;
    }

    .product-card:hover {
        transform: translateY(-5px);
    }

    /* Hiệu ứng cho hình ảnh sản phẩm */
    .product-card .product-image img {
        transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .product-card:hover .product-image img {
        transform: scale(1.08);
    }

    /* Hiệu ứng cho nút thao tác */
    .product-card .product-actions button,
    .product-card .product-actions a {
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .product-card .product-actions button:hover,
    .product-card .product-actions a:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Màu hover cho tiêu đề sản phẩm */
    .product-title-link.regular-product:hover h3 {
        color: #3B82F6;
        /* Màu xanh cho sản phẩm thường */
    }

    .product-title-link.quote-product:hover h3 {
        color: #F37321;
        /* Màu cam cho sản phẩm báo giá */
    }

    /* Thiết kế tinh tế hơn cho thẻ "Giá theo yêu cầu" */
    .price-on-request {
        font-size: 0.75rem;
        letter-spacing: 0.01em;
        box-shadow: none;
        background-opacity: 0.8;
        transition: all 0.3s ease;
    }

    .price-on-request i {
        font-size: 0.7rem;
        opacity: 0.9;
    }

    @media (max-width: 767px) {
        .price-on-request {
            font-size: 0.7rem;
            padding: 0.15rem 0.5rem;
            border-radius: 0.25rem;
        }

        .price-on-request i {
            font-size: 0.65rem;
            margin-right: 0.25rem;
        }
    }

    /* Đảm bảo nút thêm giỏ hàng và báo giá luôn hiển thị trên tất cả các thiết bị */
    .product-card .product-actions .bg-primary,
    .product-card .product-actions .bg-gradient-to-r,
    .product-card .product-actions .add-to-cart-btn {
        display: flex !important;
        opacity: 1 !important;
    }

    /* CSS cho danh mục sản phẩm - Đã được thay thế bằng Tailwind */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-up {
        animation: fadeInUp 0.8s ease forwards;
    }

    /* CSS cho grid sản phẩm mới */
    .modern-product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 2rem;
    }

    /* CSS cho card sản phẩm mới */
    .modern-product-card {
        background-color: var(--color-white);
        border-radius: var(--radius-xl);
        overflow: hidden;
        box-shadow: var(--shadow);
        transition: all 0.3s ease;
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .modern-product-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    /* Đảm bảo hình ảnh hiển thị đúng */
    .modern-product-card .product-image-wrapper {
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    /* Đảm bảo hình ảnh hiển thị đúng trên Safari */
    @media not all and (min-resolution:.001dpcm) {
        @supports (-webkit-appearance:none) {
            .modern-product-card .product-image-wrapper img.product-image {
                height: 100% !important;
                width: 100% !important;
                object-fit: cover !important;
            }
        }
    }

    /* Fix cho IE và một số trình duyệt cũ */
    @media all and (-ms-high-contrast: none),
    (-ms-high-contrast: active) {
        .product-image-wrapper {
            height: 0;
        }

        .product-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }

    /* Phần hình ảnh sản phẩm - Thiết kế đơn giản và tinh tế hơn */
    .simple-product-image {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 100%;
        /* Tỷ lệ 1:1 */
        overflow: hidden;
        background-color: #f9f9f9;
        border-top-left-radius: var(--radius-xl);
        border-top-right-radius: var(--radius-xl);
        background-image: linear-gradient(110deg, #f8f8f8 8%, #ffffff 18%, #f8f8f8 33%);
        background-size: 200% 100%;
        animation: 1.5s shine linear infinite;
        box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.03);
    }

    @keyframes shine {
        to {
            background-position-x: -200%;
        }
    }

    /* Đảm bảo hình ảnh hiển thị đúng trên tất cả các trình duyệt */
    @media all and (-ms-high-contrast: none),
    (-ms-high-contrast: active) {

        /* IE10+ CSS */
        .simple-product-image {
            height: 280px;
        }
    }

    @supports (-ms-ime-align:auto) {

        /* Edge CSS */
        .simple-product-image {
            height: 280px;
        }
    }

    @-moz-document url-prefix() {

        /* Firefox CSS */
        .simple-image {
            background-size: cover !important;
            background-position: center !important;
        }
    }

    .simple-image-link {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: block;
        z-index: 1;
    }

    .simple-image {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-size: cover !important;
        background-position: center !important;
        transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1) !important;
        z-index: 1 !important;
    }

    /* Overlay cho hiệu ứng hover */
    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 70%, rgba(0, 0, 0, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.6s ease;
        z-index: 2;
    }

    .modern-product-card:hover .simple-image {
        transform: scale(1.08);
    }

    .modern-product-card:hover .image-overlay {
        opacity: 1;
    }

    .simple-no-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        color: #aaa;
        font-size: 2rem;
    }

    /* Badge giảm giá - Thiết kế mới tinh tế hơn */
    .discount-badge {
        position: absolute;
        top: 1rem;
        left: 1rem;
        background: linear-gradient(135deg, var(--color-danger) 0%, #ff6b6b 100%);
        color: var(--color-white);
        font-weight: 600;
        font-size: 0.75rem;
        padding: 0.35rem 0.85rem;
        border-radius: var(--radius-full);
        z-index: 10;
        box-shadow: 0 4px 8px rgba(var(--color-danger-rgb), 0.3);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(4px);
    }

    .modern-product-card:hover .discount-badge {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 6px 12px rgba(var(--color-danger-rgb), 0.4);
    }

    /* Trạng thái sản phẩm - Thiết kế mới tinh tế hơn */
    .stock-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.35rem 0.85rem;
        border-radius: var(--radius-full);
        z-index: 10;
        display: flex;
        align-items: center;
        gap: 0.35rem;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(4px);
    }

    .modern-product-card:hover .stock-status {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }

    .in-stock {
        background: linear-gradient(135deg, var(--color-success) 0%, #34d399 100%);
        color: white;
        font-weight: 600;
        box-shadow: 0 3px 6px rgba(var(--color-success-rgb), 0.3);
    }

    .out-of-stock {
        background: linear-gradient(135deg, var(--color-danger) 0%, #f87171 100%);
        color: white;
        font-weight: 600;
        box-shadow: 0 3px 6px rgba(var(--color-danger-rgb), 0.3);
    }



    /* Nút hành động nhanh - Thiết kế mới tinh tế hơn */
    .quick-actions {
        position: absolute;
        bottom: 1rem;
        left: 50%;
        transform: translateX(-50%) translateY(20px);
        display: flex;
        gap: 0.75rem;
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
        z-index: 20;
    }

    .modern-product-card:hover .quick-actions {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }

    .quick-action-btn {
        width: 2.75rem;
        height: 2.75rem;
        border-radius: var(--radius-full);
        background-color: var(--color-white);
        color: var(--color-dark);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        border: none;
        cursor: pointer;
        overflow: hidden;
    }

    .quick-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .quick-action-btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    .quick-action-btn:hover::before {
        opacity: 0.5;
    }

    .quick-action-btn.cart-btn {
        background-color: var(--color-primary);
        color: var(--color-white);
    }

    .quick-action-btn.contact-btn {
        background-color: var(--color-info);
        color: var(--color-white);
    }

    .quick-action-btn .tooltip {
        position: absolute;
        top: -2.5rem;
        left: 50%;
        transform: translateX(-50%);
        background-color: var(--color-dark);
        color: var(--color-white);
        font-size: 0.75rem;
        padding: 0.35rem 0.85rem;
        border-radius: var(--radius-md);
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .quick-action-btn:hover .tooltip {
        opacity: 1;
        visibility: visible;
    }

    /* Phần thông tin sản phẩm - Match search page exactly */
    .product-info-wrapper {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        padding: 1rem 1rem 0.75rem 1rem;
        /* Match search page actual applied padding */
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
        position: relative;
    }

    /* Match search page pseudo-element */
    .product-info-wrapper::before {
        content: '';
        position: absolute;
        top: 0;
        left: 1.5rem;
        right: 1.5rem;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
    }

    .modern-product-card:hover .product-info-wrapper {
        background-color: #fcfcfc;
    }

    .product-category {
        font-size: 0.75rem;
        color: var(--color-primary);
        background-color: rgba(var(--color-primary-rgb), 0.08);
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-full);
        display: inline-flex;
        align-items: center;
        align-self: flex-start;
        transition: all 0.3s ease;
        margin-top: 0.25rem;
        border: 1px solid rgba(var(--color-primary-rgb), 0.1);
    }

    .product-category:hover {
        background-color: rgba(var(--color-primary-rgb), 0.15);
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(var(--color-primary-rgb), 0.1);
    }

    .product-title {
        display: block;
        margin-bottom: 0 !important;
        /* Remove gap between title and price */
    }

    .premium-price-section {
        margin-top: 0;
        margin-bottom: 0;
        /* No gap before rating section */
    }

    .product-rating-sales {
        margin-top: 0.5rem;
        /* Consistent reduced spacing */
    }

    .product-title h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--color-dark);
        margin: 0;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        transition: color 0.3s ease;
        min-height: 2.8rem;
    }

    .product-rating {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: var(--color-warning);
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
    }

    .product-rating i {
        transition: transform 0.3s ease;
    }

    .modern-product-card:hover .product-rating i {
        transform: rotate(360deg);
    }

    .rating-count {
        color: var(--color-dark);
        opacity: 0.6;
        font-size: 0.75rem;
        margin-left: 0.25rem;
    }

    /* Premium Price Section - Match search page */
    .premium-price-section {
        background: none;
        border: none;
        border-radius: 0;
        padding: 0;
        position: relative;
        margin-top: auto;
        /* Match search page - đẩy xuống dưới cùng */
        min-height: 4.5rem;
        /* Đảm bảo chiều cao tối thiểu nhất quán */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Product Rating and Sales Section - Match search page */
    .product-rating-sales {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem;
        /* Consistent reduced spacing */
        padding: 0;
        border-top: none;
        width: 100%;
    }

    .rating-section {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        flex-shrink: 0;
    }

    .rating-section .stars {
        display: flex;
        gap: 0.125rem;
    }

    .rating-section .stars i {
        font-size: 0.75rem;
        color: #fbbf24;
    }

    .rating-section .rating-text {
        font-size: 0.75rem;
        color: #64748b;
        font-weight: 500;
    }

    .sales-section {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        flex-shrink: 0;
    }

    .sales-section i {
        font-size: 0.75rem;
        color: #3b82f6;
    }

    .sales-section span {
        font-size: 0.75rem;
        color: #64748b;
        font-weight: 500;
    }

    .product-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.75rem;
        color: var(--color-dark);
        opacity: 0.7;
        background-color: rgba(0, 0, 0, 0.02);
        padding: 0.5rem 0.75rem;
        border-radius: var(--radius-md);
        border: 1px solid rgba(0, 0, 0, 0.03);
        transition: all 0.3s ease;
    }

    .modern-product-card:hover .product-meta {
        background-color: rgba(0, 0, 0, 0.03);
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .product-price {
        margin-top: 0.75rem;
        padding: 0.75rem;
        background-color: rgba(0, 0, 0, 0.03);
        border-radius: var(--radius-lg);
        text-align: center;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .modern-product-card:hover .product-price {
        background-color: rgba(0, 0, 0, 0.04);
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
    }

    .contact-price {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: var(--color-info);
        font-weight: 600;
        font-size: 0.9rem;
        background-color: rgba(var(--color-info-rgb), 0.1);
        padding: 0.5rem 0.75rem;
        border-radius: var(--radius-md);
        width: 100%;
        box-shadow: 0 2px 4px rgba(var(--color-info-rgb), 0.1);
        transition: all 0.3s ease;
    }

    .modern-product-card:hover .contact-price {
        background-color: rgba(var(--color-info-rgb), 0.15);
    }

    .sale-price {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .old-price {
        font-size: 0.875rem;
        color: var(--color-dark);
        opacity: 0.6;
        text-decoration: line-through;
        margin-bottom: 0.25rem;
    }

    .current-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--color-primary);
        text-shadow: 0 1px 1px rgba(var(--color-primary-rgb), 0.1);
    }

    .regular-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--color-dark);
        text-align: center;
    }



    /* Hiệu ứng cho nút và tương tác */
    .category-tab-btn.active {
        animation: pulse 0.5s ease-out;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.7);
        }

        70% {
            box-shadow: 0 0 0 10px rgba(var(--color-primary-rgb), 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0);
        }
    }

    .modern-product-card.hovered {
        z-index: 10;
    }

    .add-to-cart-btn.clicked,
    .main-action-btn.clicked {
        animation: buttonClick 0.3s ease-out;
    }

    @keyframes buttonClick {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(0.95);
        }

        100% {
            transform: scale(1);
        }
    }

    /* Hiệu ứng bay vào giỏ hàng */
    .fly-item {
        position: absolute;
        width: 50px;
        height: 50px;
        background-size: cover;
        background-position: center;
        border-radius: 50%;
        z-index: 9999;
        box-shadow: var(--shadow-md);
        pointer-events: none;
        transition: all 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    }

    /* Hiệu ứng loading cho nút thêm giỏ hàng */
    .add-to-cart-btn.loading,
    .main-action-btn.loading {
        position: relative;
        color: transparent !important;
        pointer-events: none;
    }

    .add-to-cart-btn.loading::after,
    .main-action-btn.loading::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 0.8s linear infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    /* Responsive cho thiết kế mới - Tối ưu hóa cho thiết bị di động */
    @media (max-width: 767px) {
        .category-tabs {
            gap: 0.5rem;
            flex-wrap: nowrap;
            overflow-x: auto;
            padding-bottom: 0.5rem;
            justify-content: flex-start;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            /* Firefox */
        }

        .category-tabs::-webkit-scrollbar {
            display: none;
            /* Chrome, Safari, Edge */
        }

        .category-tab-btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }

        .modern-product-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            padding: 0 0.25rem;
        }

        .modern-product-card {
            border-radius: var(--radius-lg);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
        }

        /* Tối ưu hiển thị hình ảnh sản phẩm trên điện thoại */
        .simple-product-image {
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }

        .simple-image {
            background-size: cover;
            background-position: center;
        }

        /* Tối ưu hiển thị badge và trạng thái */
        .discount-badge {
            font-size: 0.65rem;
            padding: 0.2rem 0.5rem;
            top: 0.5rem;
            left: 0.5rem;
        }

        .stock-status {
            font-size: 0.65rem;
            padding: 0.2rem 0.5rem;
            top: 0.5rem;
            right: 0.5rem;
            gap: 0.2rem;
        }

        .stock-status i {
            font-size: 0.6rem;
        }

        /* Tối ưu hiển thị thông tin sản phẩm - Match search page mobile */

        .product-info-wrapper::before {
            left: 1rem;
            right: 1rem;
        }

        .product-title h3 {
            font-size: 0.85rem;
            min-height: 2.4rem;
            line-height: 1.4;
            margin-bottom: 0.2rem;
        }

        .product-category {
            font-size: 0.65rem;
            padding: 0.15rem 0.5rem;
        }

        .product-meta {
            display: none;
        }

        .product-rating {
            font-size: 0.7rem;
        }

        .rating-count {
            font-size: 0.65rem;
        }

        .product-price {
            margin-top: 0.5rem;
            padding: 0.5rem;
        }

        .current-price,
        .regular-price {
            font-size: 0.95rem;
        }

        .old-price {
            font-size: 0.75rem;
        }

        .contact-price {
            font-size: 0.8rem;
            padding: 0.35rem 0.5rem;
        }

        /* ===== OVERRIDE CONTACT PRICE CONTAINER CHO MOBILE ===== */
        .contact-price-container {
            display: flex !important;
            flex-direction: column !important;
            align-items: flex-start !important;
            justify-content: center !important;
            gap: 0.375rem !important;
            width: 100% !important;
            height: auto !important;
            background: none !important;
            border: none !important;
            border-radius: 0 !important;
            position: relative !important;
            overflow: visible !important;
        }

        .contact-price-main {
            font-size: 1rem !important;
            /* Mobile size */
        }

        .contact-price-main i {
            font-size: 0.875rem !important;
            /* Mobile icon size */
        }

        .contact-price-subtitle {
            font-size: 0.6rem !important;
            /* Mobile subtitle */
        }

        /* Tối ưu hiển thị nút thao tác */
        .main-action-btn {
            padding: 0.5rem;
            font-size: 0.875rem;
        }

        .quick-actions {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
            bottom: 0.5rem;
            gap: 0.5rem;
        }

        .quick-action-btn {
            width: 2rem;
            height: 2rem;
            font-size: 0.75rem;
        }

        .quick-action-btn .tooltip {
            display: none;
        }

        /* Cải thiện hiệu ứng hover trên mobile */
        .modern-product-card:active {
            transform: scale(0.98);
        }

        .modern-product-card:hover .product-price,
        .modern-product-card:hover .discount-badge,
        .modern-product-card:hover .stock-status {
            transform: none;
        }
    }

    /* Responsive cho tiêu đề danh mục */
    @media (max-width: 767px) {
        .category-header {
            margin-bottom: 1.5rem;
        }

        .category-header .flex {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .category-header .bg-gradient-to-b {
            padding-right: 0;
            width: 100%;
        }

        .category-header h3 {
            font-size: 1.25rem;
            flex-direction: column;
            align-items: flex-start;
        }

        .category-header h3 span {
            margin-left: 0;
            margin-top: 0.5rem;
        }
    }

    /* CSS cho category products slider */
    .category-products-slider {
        position: relative;
        padding: 0 10px;
        margin: 0 -10px;
        overflow: hidden;
    }

    .category-products-slider .swiper-container {
        overflow: visible;
        padding: 20px 0;
    }

    .category-products-slider .swiper-slide {
        height: auto;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    /* Hiệu ứng chỉ áp dụng cho điện thoại */
    @media (max-width: 767px) {

        .category-products-slider .swiper-slide,
        .featured-products-slider .swiper-slide {
            opacity: 0.85;
            transform: scale(0.95);
        }

        .category-products-slider .swiper-slide-active,
        .featured-products-slider .swiper-slide-active {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Hiệu ứng chuyển đổi mượt mà */
    .category-products-slider .swiper-wrapper,
    .featured-products-slider .swiper-wrapper {
        transition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    /* Nút điều hướng slider đã được thay thế bằng Tailwind */

    /* Responsive cho category products slider */
    @media (max-width: 767px) {
        .category-products-slider {
            margin: 0;
            padding: 0 8px;
        }

        /* Điều chỉnh badge giảm giá trên mobile */
        .sale-badge {
            top: 5px;
            left: 5px;
        }

        .sale-badge-content {
            padding: 0.2rem 0.5rem;
            font-size: 0.65rem;
        }

        .category-products-slider .swiper-container,
        .featured-products-slider .swiper-container {
            overflow: visible;
            padding: 10px 0;
        }

        .category-products-slider .swiper-slide,
        .featured-products-slider .swiper-slide {
            width: 80%;
        }

        .category-products-slider .product-card,
        .featured-products-slider .product-card {
            margin-right: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        /* Responsive cho nút điều hướng slider và phân trang dots */
        .featured-button-prev,
        .featured-button-next,
        [class*="category-button-prev-"],
        [class*="category-button-next-"] {
            @apply w-10 h-10;
        }

        .featured-slider-pagination,
        [class*="category-slider-pagination-"] {
            @apply mt-4;
        }

        /* Tối ưu hiển thị card trên điện thoại */
        .category-products-slider .product-card .p-4 {
            padding: 0.75rem;
        }

        .category-products-slider .product-card h3 {
            font-size: 0.85rem;
            line-height: 1.3;
            margin-bottom: 0.5rem;
        }

        /* Tối ưu hiển thị danh mục */
        .category-products-slider .product-card .mb-2 {
            margin-bottom: 0.25rem;
        }

        .category-products-slider .product-card .text-xs {
            font-size: 0.65rem;
        }

        /* Tối ưu hiển thị trạng thái và đánh giá */
        .category-products-slider .product-card .flex.justify-between.items-center.mb-3 {
            margin-bottom: 0.5rem;
        }

        .category-products-slider .product-card .inline-flex.items-center.px-2.py-1 {
            padding: 0.15rem 0.5rem;
            font-size: 0.65rem;
        }

        .category-products-slider .product-card .fa-star {
            font-size: 0.65rem;
        }

        /* Tối ưu hiển thị thông tin lượt bán và lượt xem */
        .category-products-slider .product-card .flex.items-center.justify-between.mb-3 {
            margin-bottom: 0.5rem;
        }

        /* Tối ưu hiển thị giá */
        .category-products-slider .product-card .price-container {
            margin-bottom: 0.5rem;
        }

        .category-products-slider .product-card .text-blue-500.font-bold,
        .category-products-slider .product-card .text-gray-500.line-through {
            font-size: 0.85rem;
        }

        /* Tối ưu hiển thị nút */
        .category-products-slider .product-card .flex.space-x-2 {
            gap: 0.35rem;
        }

        .category-products-slider .product-card .flex.space-x-2 button,
        .category-products-slider .product-card .flex.space-x-2 a {
            padding: 0.4rem 0.5rem;
            font-size: 0.75rem;
        }

        .category-products-slider .product-card .flex.space-x-2 button i,
        .category-products-slider .product-card .flex.space-x-2 a i {
            font-size: 0.75rem;
            margin-right: 0.25rem;
        }
    }

    /* Tối ưu cho điện thoại nhỏ */
    @media (max-width: 374px) {
        .category-products-slider .product-card .p-4 {
            padding: 0.5rem;
        }

        .category-products-slider .product-card h3 {
            font-size: 0.75rem;
            line-height: 1.2;
        }

        .category-products-slider .product-card .flex.space-x-2 button span,
        .category-products-slider .product-card .flex.space-x-2 a span {
            font-size: 0.7rem;
        }

        .category-products-slider .product-card .inline-flex.items-center.px-2.py-1 {
            padding: 0.1rem 0.4rem;
            font-size: 0.6rem;
        }

        .sale-badge-content {
            padding: 0.15rem 0.4rem;
            font-size: 0.6rem;
        }
    }

    /* Hiệu ứng đã được thay thế bằng Tailwind */

    /* Hiệu ứng đã được thay thế bằng Tailwind */

    /* Badge giảm giá đã được thay thế bằng Tailwind */

    /* Tùy chỉnh phân trang dots cho Swiper */
    .featured-slider-pagination .swiper-pagination-bullet,
    .featured-pagination-bullet {
        @apply w-3 h-3 bg-gray-300 rounded-full mx-1 cursor-pointer transition-all duration-300 opacity-70;
    }

    .featured-slider-pagination .swiper-pagination-bullet-active,
    .featured-pagination-bullet-active {
        @apply w-4 h-4 bg-blue-500 opacity-100;
    }

    /* Responsive cho phân trang dots */
    @media (max-width: 767px) {

        .featured-slider-pagination .swiper-pagination-bullet,
        .featured-pagination-bullet {
            @apply w-2 h-2;
        }

        .featured-slider-pagination .swiper-pagination-bullet-active,
        .featured-pagination-bullet-active {
            @apply w-3 h-3;
        }
    }

    /* Hiệu ứng nhấp nháy cho icon điện thoại */
    @keyframes pulse {
        0% {
            opacity: 0.6;
        }

        50% {
            opacity: 1;
        }

        100% {
            opacity: 0.6;
        }
    }

    .animate-pulse {
        animation: pulse 1.5s infinite ease-in-out;
    }

    /* Premium Sale Badge - Modern & Sophisticated với tông màu cam chính */
    .premium-sale-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        z-index: 10;
        background: linear-gradient(135deg, #F37321 0%, #D65A0F 50%, #D35400 100%);
        border-radius: 0.75rem;
        padding: 0.5rem 0.75rem;
        box-shadow:
            0 4px 12px rgba(243, 115, 33, 0.4),
            0 2px 4px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transform: rotate(-3deg);
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
        border: 1px solid rgba(255, 255, 255, 0.1);
        animation: premium-badge-pulse 2.5s infinite;
    }

    .premium-sale-badge:hover,
    .modern-product-card:hover .premium-sale-badge,
    .product-card:hover .premium-sale-badge,
    .group:hover .premium-sale-badge {
        transform: rotate(0deg) scale(1.05) !important;
        box-shadow:
            0 6px 16px rgba(243, 115, 33, 0.5),
            0 3px 6px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
        background: linear-gradient(135deg, #FF8A3D 0%, #F37321 50%, #D65A0F 100%) !important;
        animation: none !important;
        /* Stop pulse animation on hover */
    }

    /* Hiệu ứng pulse cho premium sale badge */
    @keyframes premium-badge-pulse {
        0% {
            box-shadow: 0 4px 12px rgba(243, 115, 33, 0.4), 0 0 0 0 rgba(243, 115, 33, 0.3);
        }

        70% {
            box-shadow: 0 4px 12px rgba(243, 115, 33, 0.4), 0 0 0 8px rgba(243, 115, 33, 0);
        }

        100% {
            box-shadow: 0 4px 12px rgba(243, 115, 33, 0.4), 0 0 0 0 rgba(243, 115, 33, 0);
        }
    }

    .badge-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.125rem;
    }

    .discount-percent {
        font-size: 0.875rem;
        font-weight: 800;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        line-height: 1;
    }

    .sale-text {
        font-size: 0.625rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        line-height: 1;
    }

    /* Responsive Design for Premium Elements */
    @media (max-width: 768px) {
        .premium-sale-badge {
            top: 0.75rem;
            right: 0.75rem;
            padding: 0.375rem 0.625rem;
            border-radius: 0.625rem;
        }

        .discount-percent {
            font-size: 0.75rem;
        }

        .sale-text {
            font-size: 0.5rem;
        }

        /* ===== OVERRIDE CONTACT PRICE CONTAINER CHO TABLET ===== */
        .contact-price-container {
            display: flex !important;
            flex-direction: column !important;
            align-items: flex-start !important;
            justify-content: center !important;
            gap: 0.375rem !important;
            width: 100% !important;
            height: auto !important;
            background: none !important;
            border: none !important;
            border-radius: 0 !important;
            position: relative !important;
            overflow: visible !important;
        }

        .contact-price-main {
    font-size: 0.75rem !important;
}

        .contact-price-subtitle {
            font-size: clamp(0.6rem, 1.25rem, 1.25rem) !important;
        }

        

        /* Đảm bảo chiều cao tối thiểu cho tablet */
        .premium-price-section {
            min-height: 4rem !important;
        }
    }

    @media (max-width: 480px) {
        .premium-sale-badge {
            top: 0.5rem;
            right: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.5rem;
        }

        .discount-percent {
            font-size: 0.625rem;
        }

        .sale-text {
            font-size: 0.4rem;
        }

        /* Đảm bảo chiều cao tối thiểu cho mobile */
        .premium-price-section {
            min-height: 3.5rem !important;
        }
    }
</style>

<!-- Script cho hiệu ứng và tương tác -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Duplicate animation code đã được xóa - sử dụng animation system thống nhất ở cuối file

        // Đảm bảo hình ảnh hiển thị đúng sau khi trang đã tải xong
        window.addEventListener('load', function () {
            console.log('Trang đã tải xong, kiểm tra hình ảnh sản phẩm');

            // Kiểm tra và sửa lỗi hiển thị hình ảnh ngay lập tức
            (function () {
                // Kiểm tra các phần tử simple-image
                document.querySelectorAll('.simple-image').forEach(img => {
                    console.log('Tìm thấy simple-image:', img);

                    // Đảm bảo background-image hiển thị đúng
                    const style = window.getComputedStyle(img);
                    const bgImage = style.backgroundImage;
                    console.log('Background image:', bgImage);

                    if (!bgImage || bgImage === 'none') {
                        console.log('Background image không hợp lệ, thử lấy từ thuộc tính style');
                        const inlineStyle = img.getAttribute('style');
                        console.log('Inline style:', inlineStyle);

                        // Thử sửa lỗi bằng cách thêm lại style
                        if (inlineStyle && inlineStyle.includes('background-image')) {
                            // Đã có style nhưng không hiển thị, thử thêm lại
                            img.style.backgroundSize = 'cover';
                            img.style.backgroundPosition = 'center';
                            img.style.position = 'absolute';
                            img.style.top = '0';
                            img.style.left = '0';
                            img.style.width = '100%';
                            img.style.height = '100%';
                            img.style.zIndex = '1';
                        }
                    }
                });

                // Kiểm tra các phần tử img.product-image
                document.querySelectorAll('.product-image').forEach(img => {
                    console.log('Tìm thấy product-image:', img);
                    console.log('Src:', img.src);

                    img.style.opacity = '1';
                    img.style.visibility = 'visible';
                    img.style.display = 'block';
                });

                // Thêm debug để kiểm tra URL hình ảnh
                document.querySelectorAll('.modern-product-card').forEach(card => {
                    const productId = card.querySelector('.add-to-cart-btn')?.dataset.productId;
                    const simpleImage = card.querySelector('.simple-image');

                    if (simpleImage) {
                        const style = simpleImage.getAttribute('style');
                        console.log(`Product ID: ${productId}, Image style: ${style}`);

                        // Thử sửa lỗi bằng cách thêm lại style
                        simpleImage.style.backgroundSize = 'cover';
                        simpleImage.style.backgroundPosition = 'center';
                        simpleImage.style.position = 'absolute';
                        simpleImage.style.top = '0';
                        simpleImage.style.left = '0';
                        simpleImage.style.width = '100%';
                        simpleImage.style.height = '100%';
                        simpleImage.style.zIndex = '1';

                        // Dừng hiệu ứng loading
                        const productImage = card.querySelector('.simple-product-image');
                        if (productImage) {
                            productImage.style.animation = 'none';
                            productImage.style.backgroundImage = 'none';
                        }
                    }
                });

                // Thực hiện lại kiểm tra sau 500ms để đảm bảo hình ảnh hiển thị đúng
                setTimeout(function () {
                    document.querySelectorAll('.simple-image').forEach(img => {
                        // Đảm bảo background-image hiển thị đúng
                        img.style.backgroundSize = 'cover';
                        img.style.backgroundPosition = 'center';
                        img.style.position = 'absolute';
                        img.style.top = '0';
                        img.style.left = '0';
                        img.style.width = '100%';
                        img.style.height = '100%';
                        img.style.zIndex = '1';

                        // Dừng hiệu ứng loading cho container
                        const productImage = img.closest('.simple-product-image');
                        if (productImage) {
                            productImage.style.animation = 'none';
                            productImage.style.backgroundImage = 'none';
                        }
                    });
                }, 500);
            })();
        });



        // Đảm bảo hình ảnh sản phẩm được tải đúng
        const productImages = document.querySelectorAll('.product-image');

        productImages.forEach(img => {
            // Xử lý lỗi tải hình ảnh
            img.addEventListener('error', function () {
                console.log('Lỗi tải hình ảnh:', this.src);
                const parent = this.parentElement;

                // Tạo phần tử thay thế
                const fallbackImage = document.createElement('div');
                fallbackImage.className = 'no-image';
                fallbackImage.innerHTML = '<i class="fas fa-image"></i>';

                // Thay thế hình ảnh bị lỗi
                this.style.display = 'none';
                parent.appendChild(fallbackImage);
            });

            // Đảm bảo hình ảnh đã tải xong
            if (img.complete) {
                console.log('Hình ảnh đã tải xong:', img.src);
                img.style.opacity = '1';
                const wrapper = img.closest('.product-image-wrapper');
                if (wrapper) {
                    wrapper.style.animation = 'none';
                    wrapper.style.backgroundImage = 'none';
                }
            } else {
                img.addEventListener('load', function () {
                    console.log('Hình ảnh vừa tải xong:', this.src);
                    this.style.opacity = '1';
                    const wrapper = this.closest('.product-image-wrapper');
                    if (wrapper) {
                        wrapper.style.animation = 'none';
                        wrapper.style.backgroundImage = 'none';
                    }
                });
            }
        });

        // Kiểm tra và sửa lỗi hiển thị hình ảnh
        setTimeout(() => {
            document.querySelectorAll('.product-image-wrapper').forEach(wrapper => {
                const img = wrapper.querySelector('img.product-image');
                if (img) {
                    // Đảm bảo hình ảnh hiển thị
                    img.style.opacity = '1';
                    img.style.visibility = 'visible';
                    img.style.display = 'block';

                    // Dừng animation loading
                    wrapper.style.animation = 'none';
                    wrapper.style.backgroundImage = 'none';
                }
            });
        }, 1000);

        // Hiệu ứng hover cho card sản phẩm
        const productCards = document.querySelectorAll('.modern-product-card');

        productCards.forEach(card => {
            card.addEventListener('mouseenter', function () {
                this.classList.add('hovered');
            });

            card.addEventListener('mouseleave', function () {
                this.classList.remove('hovered');
            });
        });

        // LOẠI BỎ event listener trùng lặp - đã được xử lý trong cart-realtime.js
        // const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
        // addToCartButtons.forEach(button => {
        //     button.addEventListener('click', function(e) {
        //         e.preventDefault();
        //         this.classList.add('clicked');
        //         const productId = this.getAttribute('data-product-id');
        //         flyToCart(this);
        //         setTimeout(() => {
        //             this.classList.remove('clicked');
        //         }, 300);
        //     });
        // });

        // Hàm tạo hiệu ứng bay vào giỏ hàng
        function flyToCart(button) {
            // Tìm hình ảnh sản phẩm gần nhất
            let productCard = button.closest('.modern-product-card');
            if (!productCard) {
                productCard = button.closest('.product-card');
            }

            if (!productCard) return;

            // Tìm hình ảnh sản phẩm (sử dụng div với background-image)
            const simpleImage = productCard.querySelector('.simple-image');
            const productImage = productCard.querySelector('img');

            let imageUrl = '';
            let imageElement = null;

            if (simpleImage) {
                // Lấy URL từ background-image
                const style = window.getComputedStyle(simpleImage);
                const bgImage = style.backgroundImage;
                imageUrl = bgImage.replace(/url\(['"]?(.*?)['"]?\)/i, '$1');
                imageElement = simpleImage;
            } else if (productImage) {
                // Sử dụng hình ảnh thông thường
                imageUrl = productImage.src;
                imageElement = productImage;
            } else {
                return; // Không tìm thấy hình ảnh
            }

            // Tìm giỏ hàng
            const cart = document.querySelector('.cart-btn') || document.querySelector('.cart-icon');
            if (!cart) return;

            // Tạo phần tử bay
            const flyItem = document.createElement('div');
            flyItem.className = 'fly-item';
            flyItem.style.backgroundImage = `url(${imageUrl})`;

            // Lấy vị trí của hình ảnh sản phẩm
            const productRect = imageElement.getBoundingClientRect();

            // Lấy vị trí của giỏ hàng
            const cartRect = cart.getBoundingClientRect();

            // Đặt vị trí ban đầu của phần tử bay
            flyItem.style.top = `${productRect.top + window.scrollY}px`;
            flyItem.style.left = `${productRect.left + window.scrollX}px`;
            flyItem.style.width = `${productRect.width / 2}px`;
            flyItem.style.height = `${productRect.height / 2}px`;

            // Thêm phần tử bay vào body
            document.body.appendChild(flyItem);

            // Đặt vị trí đích của phần tử bay
            setTimeout(() => {
                flyItem.style.top = `${cartRect.top + window.scrollY}px`;
                flyItem.style.left = `${cartRect.left + window.scrollX}px`;
                flyItem.style.width = '20px';
                flyItem.style.height = '20px';
                flyItem.style.opacity = '0.5';

                // Thêm hiệu ứng cho giỏ hàng
                cart.classList.add('pulse-once');

                // Xóa phần tử bay sau khi hoàn thành
                setTimeout(() => {
                    flyItem.remove();
                    cart.classList.remove('pulse-once');
                }, 700);
            }, 10);
        }
    });
</script>

<!-- Cảm nhận khách hàng -->
<div class="section-container">
    <div class="testimonials-section">
        <!-- Background pattern -->
        <div class="testimonials-bg"></div>

        <div class="container mx-auto px-4">
            <div class="testimonials-title-container">
                <div class="testimonials-badge">
                    <span class="badge-icon"><i class="fas fa-quote-left"></i></span>
                    Khách hàng nói gì về chúng tôi
                </div>

                <h2 class="testimonials-heading">
                    Cảm Nhận Khách Hàng
                </h2>

                <p class="testimonials-description">
                    Những đánh giá chân thực từ khách hàng đã sử dụng sản phẩm và dịch vụ của Nội Thất Bàng Vũ
                </p>
            </div>

            <div class="testimonials-container">
                <!-- Swiper container -->
                <div class="swiper testimonials-swiper">
                    <div class="swiper-wrapper">
                        <?php
                        // Include helper cho testimonials
                        include_once 'includes/testimonials-helper.php';

                        // Lấy danh sách cảm nhận khách hàng
                        $testimonials = get_testimonials(6, 0, 1);

                        if (!empty($testimonials)):
                            foreach ($testimonials as $testimonial):
                                ?>
                                <div class="swiper-slide">
                                    <div class="testimonial-card">
                                        <!-- Phần đánh giá sao và tên sản phẩm -->
                                        <div class="testimonial-product">
                                            <div class="testimonial-rating-stars">
                                                <?php
                                                $rating = intval($testimonial['rating']);
                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= $rating) {
                                                        echo '<i class="fas fa-star"></i>';
                                                    } else {
                                                        echo '<i class="far fa-star"></i>';
                                                    }
                                                }
                                                ?>
                                            </div>

                                            <?php if (!empty($testimonial['product_tags']) && count($testimonial['product_tags']) > 0): ?>
                                                <div class="product-name">
                                                    "<?php echo htmlspecialchars($testimonial['product_tags'][0]); ?>"
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Nội dung đánh giá -->
                                        <div class="testimonial-content">
                                            "<?php echo htmlspecialchars($testimonial['content']); ?>"
                                        </div>

                                        <!-- Thông tin khách hàng -->
                                        <div class="testimonial-customer">
                                            <div class="testimonial-photo">
                                                <?php if (!empty($testimonial['customer_photo'])): ?>
                                                    <img src="<?php echo BASE_URL; ?>/uploads/testimonials/<?php echo $testimonial['customer_photo']; ?>"
                                                        alt="<?php echo htmlspecialchars($testimonial['customer_name']); ?>">
                                                <?php else: ?>
                                                    <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.jpg"
                                                        alt="<?php echo htmlspecialchars($testimonial['customer_name']); ?>">
                                                <?php endif; ?>
                                            </div>

                                            <div class="testimonial-info">
                                                <h4 class="testimonial-name">
                                                    <?php echo htmlspecialchars($testimonial['customer_name']); ?></h4>

                                                <div class="testimonial-meta">
                                                    <?php if (!empty($testimonial['customer_address'])): ?>
                                                        <div class="testimonial-location">
                                                            <i class="fas fa-map-marker-alt"></i>
                                                            <span><?php echo htmlspecialchars($testimonial['customer_address']); ?></span>
                                                        </div>
                                                    <?php endif; ?>

                                                    <?php if (!empty($testimonial['customer_age'])): ?>
                                                        <div class="testimonial-age">
                                                            <i class="fas fa-birthday-cake"></i>
                                                            <span><?php echo intval($testimonial['customer_age']); ?> tuổi</span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Hiển thị ảnh đánh giá -->
                                        <?php if (!empty($testimonial['review_photos'])): ?>
                                            <div class="testimonial-media">
                                                <?php
                                                $photos = explode(',', $testimonial['review_photos']);
                                                $photos = array_filter($photos); // Lọc bỏ các giá trị trống
                                                $totalPhotos = count($photos);

                                                if ($totalPhotos > 0):
                                                    ?>
                                                    <div class="testimonial-photos-grid">
                                                        <?php
                                                        // Hiển thị tối đa 4 ảnh
                                                        for ($i = 0; $i < min(4, $totalPhotos); $i++):
                                                            $photo = $photos[$i];
                                                            $photoUrl = BASE_URL . '/uploads/testimonials/' . $photo;
                                                            $isLastPhoto = ($i == 3 && $totalPhotos > 4);
                                                            $remainingPhotos = $totalPhotos - 4;
                                                            ?>
                                                            <div class="testimonial-photo-box" style="overflow: visible !important;">
                                                                <a href="<?php echo $photoUrl; ?>" class="testimonial-photo-link"
                                                                    data-fancybox="testimonial-<?php echo $testimonial['id']; ?>"
                                                                    style="overflow: visible !important;">
                                                                    <img src="<?php echo $photoUrl; ?>"
                                                                        alt="Ảnh đánh giá <?php echo $i + 1; ?>" loading="lazy">
                                                                    <?php if ($isLastPhoto && $remainingPhotos > 0): ?>
                                                                        <div class="photo-overlay">
                                                                            <span class="overlay-text">+ <?php echo $remainingPhotos; ?>
                                                                                ảnh</span>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </a>
                                                            </div>
                                                        <?php endfor; ?>
                                                    </div>

                                                    <!-- Ẩn các ảnh còn lại cho fancybox -->
                                                    <?php if ($totalPhotos > 4): ?>
                                                        <?php for ($i = 4; $i < $totalPhotos; $i++): ?>
                                                            <?php $photoUrl = BASE_URL . '/uploads/testimonials/' . $photos[$i]; ?>
                                                            <a href="<?php echo $photoUrl; ?>"
                                                                data-fancybox="testimonial-<?php echo $testimonial['id']; ?>"
                                                                style="display: none;">
                                                                <img src="<?php echo $photoUrl; ?>" alt="Ảnh đánh giá <?php echo $i + 1; ?>"
                                                                    style="display: none;">
                                                            </a>
                                                        <?php endfor; ?>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Hiển thị video -->
                                        <?php if (!empty($testimonial['customer_video'])): ?>
                                            <div class="testimonial-video">
                                                <a href="#" class="video-trigger"
                                                    data-video-url="<?php echo htmlspecialchars($testimonial['customer_video']); ?>">
                                                    <?php if (!empty($testimonial['customer_video_thumbnail'])): ?>
                                                        <div class="video-placeholder with-thumbnail"
                                                            style="background-image: url('<?php echo BASE_URL; ?>/uploads/testimonials/<?php echo htmlspecialchars($testimonial['customer_video_thumbnail']); ?>');">
                                                            <i class="fas fa-play-circle"></i>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="video-placeholder">
                                                            <i class="fas fa-play-circle"></i>
                                                            <span>Xem video</span>
                                                        </div>
                                                    <?php endif; ?>
                                                </a>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Hiển thị các video bổ sung -->
                                        <?php if (!empty($testimonial['review_videos'])): ?>
                                            <div class="testimonial-more-videos">
                                                <?php
                                                $videos = explode(',', $testimonial['review_videos']);
                                                $thumbnails = !empty($testimonial['review_videos_thumbnails']) ? explode(',', $testimonial['review_videos_thumbnails']) : [];
                                                $totalVideos = count($videos);
                                                if ($totalVideos > 0):
                                                    ?>
                                                    <a href="#" class="more-videos-link">
                                                        <i class="fas fa-film"></i> Xem thêm <?php echo $totalVideos; ?> video khác
                                                    </a>
                                                    <div class="hidden-videos">
                                                        <?php foreach ($videos as $index => $videoUrl): ?>
                                                            <a href="#" class="video-trigger"
                                                                data-video-url="<?php echo htmlspecialchars($videoUrl); ?>" <?php if (isset($thumbnails[$index]) && !empty($thumbnails[$index])): ?>
                                                                    data-thumbnail="<?php echo BASE_URL; ?>/uploads/testimonials/<?php echo htmlspecialchars($thumbnails[$index]); ?>"
                                                                <?php endif; ?>></a>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php
                            endforeach;
                        else:
                            ?>
                            <div class="swiper-slide">
                                <div class="col-span-3 text-center py-10">
                                    <div class="text-gray-400 mb-4">
                                        <i class="fas fa-comment-dots text-5xl"></i>
                                    </div>
                                    <h3 class="text-xl font-semibold text-gray-700 mb-2">Chưa có cảm nhận khách hàng nào
                                    </h3>
                                    <p class="text-gray-500">Hãy là người đầu tiên chia sẻ trải nghiệm của bạn với chúng
                                        tôi.</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Thêm điều hướng và phân trang -->
                    <div class="swiper-pagination testimonials-pagination"></div>
                    <div class="swiper-button-next testimonials-button-next"></div>
                    <div class="swiper-button-prev testimonials-button-prev"></div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Phần Cẩm Nang Nội Thất -->
<div class="interior-handbook-section">
    <div class="handbook-bg"></div>
    <div class="handbook-container">
        <!-- Header -->
        <div class="handbook-header">
            <div class="handbook-badge fade-in-up">
                <span class="badge-icon"><i class="fas fa-book-open"></i></span>
                <span>Cẩm nang nội thất</span>
            </div>
            <h2 class="handbook-heading fade-in-up">Khám phá không gian sống lý tưởng</h2>
            <p class="handbook-description fade-in-up">Tổng hợp những kiến thức, ý tưởng và xu hướng thiết kế nội thất
                mới nhất giúp bạn tạo nên không gian sống hoàn hảo.</p>
        </div>

        <!-- Featured Article -->
        <div class="handbook-featured fade-in-up">
            <div class="handbook-featured-image">
                <?php
                // Lấy bài viết nổi bật từ blog
                include_once 'includes/blog-functions.php';
                $featured_posts = get_blog_posts(1, 0, null, null, 1);
                $featured_post = !empty($featured_posts) ? $featured_posts[0] : null;

                if ($featured_post):
                    ?>
                    <img src="<?php echo BASE_URL; ?>/uploads/blog/<?php echo $featured_post['featured_image']; ?>"
                        alt="<?php echo htmlspecialchars($featured_post['title']); ?>">
                <?php else: ?>
                    <img src="<?php echo BASE_URL; ?>/assets/images/blog-placeholder.jpg" alt="Cẩm nang nội thất">
                <?php endif; ?>
            </div>
            <div class="handbook-featured-content">
                <span class="handbook-featured-label">
                    <i class="fas fa-star"></i>
                    Bài viết nổi bật
                </span>
                <h3 class="handbook-featured-title">
                    <?php if ($featured_post): ?>
                        <a href="<?php echo BASE_URL; ?>/blog/<?php echo $featured_post['slug']; ?>">
                            <?php echo htmlspecialchars($featured_post['title']); ?>
                        </a>
                    <?php else: ?>
                        <a href="<?php echo BASE_URL; ?>/blog.php">
                            Cách chọn nội thất phù hợp cho không gian sống hiện đại
                        </a>
                    <?php endif; ?>
                </h3>
                <div class="handbook-featured-excerpt">
                    <?php if ($featured_post): ?>
                        <?php echo htmlspecialchars(strip_tags($featured_post['excerpt'] ?: $featured_post['content'])); ?>
                    <?php else: ?>
                        Khám phá những bí quyết giúp bạn lựa chọn nội thất phù hợp với phong cách và không gian sống. Từ
                        việc chọn màu sắc, chất liệu đến cách bố trí hợp lý cho đến những xu hướng thiết kế mới nhất trên
                        thị trường.
                    <?php endif; ?>
                </div>
                <div class="handbook-featured-meta">
                    <div class="handbook-featured-author">
                        <div class="handbook-featured-author-avatar">
                            <?php if ($featured_post && !empty($featured_post['author']['avatar'])): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/blog/authors/<?php echo $featured_post['author']['avatar']; ?>"
                                    alt="<?php echo htmlspecialchars($featured_post['author']['name']); ?>">
                            <?php else: ?>
                                <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.jpg" alt="Tác giả">
                            <?php endif; ?>
                        </div>
                        <span>
                            <?php echo $featured_post ? htmlspecialchars($featured_post['author']['name']) : 'Đội ngũ Nội Thất Bàng Vũ'; ?>
                        </span>
                    </div>
                    <div class="handbook-featured-date">
                        <i class="far fa-calendar-alt"></i>
                        <span>
                            <?php echo $featured_post ? $featured_post['formatted_date'] : date('d/m/Y'); ?>
                        </span>
                    </div>
                </div>
                <a href="<?php echo BASE_URL; ?>/blog<?php echo $featured_post ? '/' . $featured_post['slug'] : '.php'; ?>"
                    class="handbook-featured-button">
                    Đọc tiếp
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>

        <!-- Articles Grid -->
        <div class="handbook-grid">
            <?php
            // Lấy các bài viết được đánh dấu hiển thị trên trang chủ
            $homepage_posts = get_homepage_blog_posts(3, 0);

            // Nếu có bài viết hiển thị trên trang chủ
            if (!empty($homepage_posts)) {
                foreach ($homepage_posts as $post) {
                    // Bỏ qua bài viết nổi bật nếu đã hiển thị ở trên
                    if (isset($featured_post) && $post['id'] == $featured_post['id'])
                        continue;
                    ?>
                    <div class="handbook-card fade-in-up">
                        <div class="handbook-card-image">
                            <?php if (!empty($post['featured_image'])): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/blog/<?php echo $post['featured_image']; ?>"
                                    alt="<?php echo htmlspecialchars($post['title']); ?>">
                            <?php else: ?>
                                <img src="<?php echo BASE_URL; ?>/assets/images/blog-placeholder.jpg"
                                    alt="<?php echo htmlspecialchars($post['title']); ?>">
                            <?php endif; ?>
                        </div>
                        <div class="handbook-card-content">
                            <?php if (!empty($post['categories'])): ?>
                                <span class="handbook-card-category">
                                    <i class="fas fa-tag"></i>
                                    <?php echo htmlspecialchars($post['categories'][0]['name']); ?>
                                </span>
                            <?php endif; ?>
                            <h3 class="handbook-card-title">
                                <a href="<?php echo BASE_URL; ?>/blog/<?php echo $post['slug']; ?>">
                                    <?php echo htmlspecialchars($post['title']); ?>
                                </a>
                            </h3>
                            <div class="handbook-card-excerpt">
                                <?php echo htmlspecialchars(strip_tags($post['excerpt'] ?: $post['content'])); ?>
                            </div>
                            <div class="handbook-card-meta">
                                <div class="handbook-card-date">
                                    <i class="far fa-calendar-alt"></i>
                                    <span><?php echo $post['formatted_date']; ?></span>
                                </div>
                                <div class="handbook-card-views">
                                    <i class="far fa-eye"></i>
                                    <span><?php echo number_format($post['view_count']); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } else {
                // Nếu không có bài viết nào được đánh dấu hiển thị trên trang chủ, lấy 3 bài viết mới nhất
                $recent_posts = get_blog_posts(3, 0);

                // Nếu không có bài viết nào, hiển thị mẫu
                if (empty($recent_posts)) {
                    // Mảng dữ liệu mẫu
                    $sample_posts = [
                        [
                            'title' => 'Xu hướng thiết kế nội thất năm 2023',
                            'excerpt' => 'Khám phá những xu hướng thiết kế nội thất mới nhất năm 2023 với các phong cách tối giản, bền vững và đa chức năng...',
                            'category' => 'Xu hướng',
                            'date' => date('d/m/Y', strtotime('-2 days')),
                            'views' => rand(50, 200)
                        ],
                        [
                            'title' => 'Cách bố trí phòng khách nhỏ trở nên rộng rãi',
                            'excerpt' => 'Những mẹo thiết kế và bố trí nội thất thông minh giúp không gian phòng khách nhỏ trở nên rộng rãi và thoáng đãng hơn...',
                            'category' => 'Mẹo hay',
                            'date' => date('d/m/Y', strtotime('-5 days')),
                            'views' => rand(80, 300)
                        ],
                        [
                            'title' => 'Chọn màu sắc nội thất phù hợp với phong thủy',
                            'excerpt' => 'Hướng dẫn cách chọn màu sắc nội thất theo phong thủy để mang lại may mắn, tài lộc và sự hài hòa cho không gian sống...',
                            'category' => 'Phong thủy',
                            'date' => date('d/m/Y', strtotime('-7 days')),
                            'views' => rand(100, 400)
                        ]
                    ];

                    foreach ($sample_posts as $post) {
                        ?>
                        <div class="handbook-card">
                            <div class="handbook-card-image">
                                <img src="<?php echo BASE_URL; ?>/assets/images/blog-placeholder.jpg"
                                    alt="<?php echo htmlspecialchars($post['title']); ?>">
                            </div>
                            <div class="handbook-card-content">
                                <span class="handbook-card-category">
                                    <i class="fas fa-tag"></i>
                                    <?php echo htmlspecialchars($post['category']); ?>
                                </span>
                                <h3 class="handbook-card-title">
                                    <a href="<?php echo BASE_URL; ?>/blog.php">
                                        <?php echo htmlspecialchars($post['title']); ?>
                                    </a>
                                </h3>
                                <div class="handbook-card-excerpt">
                                    <?php echo htmlspecialchars(strip_tags($post['excerpt'])); ?>
                                </div>
                                <div class="handbook-card-meta">
                                    <div class="handbook-card-date">
                                        <i class="far fa-calendar-alt"></i>
                                        <span><?php echo $post['date']; ?></span>
                                    </div>
                                    <div class="handbook-card-views">
                                        <i class="far fa-eye"></i>
                                        <span><?php echo $post['views']; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    foreach ($recent_posts as $post) {
                        // Bỏ qua bài viết nổi bật nếu đã hiển thị ở trên
                        if (isset($featured_post) && $post['id'] == $featured_post['id'])
                            continue;
                        ?>
                        <div class="handbook-card">
                            <div class="handbook-card-image">
                                <?php if (!empty($post['featured_image'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/blog/<?php echo $post['featured_image']; ?>"
                                        alt="<?php echo htmlspecialchars($post['title']); ?>">
                                <?php else: ?>
                                    <img src="<?php echo BASE_URL; ?>/assets/images/blog-placeholder.jpg"
                                        alt="<?php echo htmlspecialchars($post['title']); ?>">
                                <?php endif; ?>
                            </div>
                            <div class="handbook-card-content">
                                <?php if (!empty($post['categories'])): ?>
                                    <span class="handbook-card-category">
                                        <i class="fas fa-tag"></i>
                                        <?php echo htmlspecialchars($post['categories'][0]['name']); ?>
                                    </span>
                                <?php endif; ?>
                                <h3 class="handbook-card-title">
                                    <a href="<?php echo BASE_URL; ?>/blog/<?php echo $post['slug']; ?>">
                                        <?php echo htmlspecialchars($post['title']); ?>
                                    </a>
                                </h3>
                                <div class="handbook-card-excerpt">
                                    <?php echo htmlspecialchars(strip_tags($post['excerpt'] ?: $post['content'])); ?>
                                </div>
                                <div class="handbook-card-meta">
                                    <div class="handbook-card-date">
                                        <i class="far fa-calendar-alt"></i>
                                        <span><?php echo $post['formatted_date']; ?></span>
                                    </div>
                                    <div class="handbook-card-views">
                                        <i class="far fa-eye"></i>
                                        <span><?php echo number_format($post['view_count']); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                }
            }
            ?>
        </div>

        <!-- View All Button -->
        <div class="text-center mt-8 fade-in-up">
            <a href="<?php echo BASE_URL; ?>/blog.php" class="inline-flex items-center justify-center px-5 sm:px-6 py-3 sm:py-3.5
                      bg-white text-gray-700 hover:text-primary
                      border border-gray-200 hover:border-primary/30
                      shadow-sm hover:shadow-lg
                      rounded-xl sm:rounded-full
                      text-sm sm:text-base font-medium
                      transition-all duration-300 transform hover:-translate-y-1 group">
                <span>Xem tất cả bài viết</span>
                <span
                    class="ml-2 w-6 h-6 rounded-full bg-gray-100 group-hover:bg-primary/10 flex items-center justify-center transition-colors duration-300">
                    <i
                        class="fas fa-arrow-right text-xs transition-transform duration-300 group-hover:translate-x-0.5"></i>
                </span>
            </a>
        </div>
    </div>
</div>



<!-- Link CSS và JS cho phần Cẩm Nang Nội Thất -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/interior-handbook.css">
<script src="<?php echo BASE_URL; ?>/assets/js/interior-handbook.js"></script>

<!-- Script Swiper JS - Load trước để banner-slider.js có thể sử dụng -->
<script src="https://unpkg.com/swiper@7/swiper-bundle.min.js"></script>

<!-- Banner slider sẽ được khởi tạo bởi banner-slider.js -->
<script src="<?php echo BASE_URL; ?>/assets/js/banner-slider.js"></script>



<?php
// Thêm script giỏ hàng thời gian thực
echo '<script src="' . BASE_URL . '/assets/js/cart-realtime.js"></script>';
?>

<!-- Thêm container cho thông báo -->
<div class="notification-container" id="notification-container"></div>



<!-- Script thông báo hiện đại đã bị vô hiệu hóa -->
<script>
    // Hàm đóng thông báo (giữ lại để tránh lỗi)
    function closeNotification(notification) {
        if (!notification) return;

        // Xóa thông báo
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }

    // Hàm tạo hiệu ứng bay vào giỏ hàng - đã vô hiệu hóa theo yêu cầu
    window.flyToCart = function flyToCart(button) {
        // Tìm vị trí của giỏ hàng - kiểm tra nhiều selector cho cả desktop và mobile
        let cartIcon = null;

        // Kiểm tra xem thiết bị là mobile hay desktop
        const isMobile = window.innerWidth < 768;

        // Tìm icon giỏ hàng của mobile trong menu item và badge
        const mobileBadge = document.querySelector('.mobile-cart-badge');
        const mobileCartIcon = document.querySelector('.mobile-cart-icon');

        // 1. Giỏ hàng trên mobile nav (ưu tiên)
        const mobileNav = document.querySelector('.mobile-bottom-nav .mobile-cart-icon') ||
            mobileCartIcon ||
            mobileBadge ||
            document.querySelector('.mobile-nav-cart') ||
            document.querySelector('.mobile-bottom-nav .fa-shopping-cart');

        // 2. Giỏ hàng trên desktop header
        const desktopCartIcon = document.querySelector('.cart-icon') ||
            document.querySelector('.header-cart') ||
            document.querySelector('.cart-badge');

        // 3. Bất kỳ phần tử nào có class chứa 'cart'
        const anyCartIcon = document.querySelector('[class*="cart"]');

        // Ưu tiên icon giỏ hàng mobile khi đang ở trên thiết bị di động
        if (isMobile && mobileNav) {
            cartIcon = mobileNav;
        } else if (desktopCartIcon) {
            cartIcon = desktopCartIcon;
        } else if (mobileNav) {
            cartIcon = mobileNav;
        } else if (anyCartIcon) {
            cartIcon = anyCartIcon;
        }

        // Chỉ thêm hiệu ứng nhấp nháy cho badge giỏ hàng, không có hiệu ứng bay - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
        if (cartIcon) {
            // Hiệu ứng bounce cho giỏ hàng - ĐÃ TẮT
            // cartIcon.classList.add('cart-bounce');
            setTimeout(() => {
                // cartIcon.classList.remove('cart-bounce');
            }, 700);
        }

        // Thêm hiệu ứng cho các thành phần giỏ hàng khác - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
        if (isMobile) {
            // Thêm hiệu ứng cho badge trong mobile menu - ĐÃ TẮT
            const mobileBadges = document.querySelectorAll('.mobile-nav-badge, .mobile-cart-badge');
            mobileBadges.forEach(badge => {
                if (badge && badge !== cartIcon) {
                    // badge.classList.add('badge-pulse');
                    setTimeout(() => {
                        // badge.classList.remove('badge-pulse');
                    }, 800);
                }
            });

            // Tìm các phần tử giỏ hàng khác trong mobile nav để thêm hiệu ứng - ĐÃ TẮT
            const otherMobileCartIcons = document.querySelectorAll('.mobile-cart-icon, .mobile-bottom-nav .fa-shopping-cart');
            otherMobileCartIcons.forEach(icon => {
                if (icon && icon !== cartIcon) {
                    // icon.classList.add('cart-bounce');
                    setTimeout(() => {
                        // icon.classList.remove('cart-bounce');
                    }, 700);
                }
            });
        }

        // Đảm bảo cập nhật tất cả badge giỏ hàng ngay lập tức
        if (typeof window.updateAllCartBadgesImmediate === 'function') {
            // Lấy số lượng từ localStorage và cập nhật ngay lập tức
            const cartCount = parseInt(localStorage.getItem('cartCount') || '0');
            window.updateAllCartBadgesImmediate(cartCount);
        } else if (typeof updateCartBadges === 'function') {
            updateCartBadges();
        }
    }

    // Hàm cập nhật tất cả các badge giỏ hàng
    function updateCartBadges() {
        // Cố gắng cập nhật badge nếu có hàm updateCartCount
        if (typeof updateCartCount === 'function') {
            updateCartCount();
        }

        // Cập nhật số lượng giỏ hàng từ localStorage
        const cartCount = parseInt(localStorage.getItem('cartCount') || '0');

        // Cập nhật tất cả các badge
        document.querySelectorAll('.cart-badge, .mobile-nav-badge, .mobile-cart-badge').forEach(badge => {
            if (cartCount > 0) {
                badge.style.display = 'flex';
                badge.textContent = cartCount > 99 ? '99+' : cartCount;
                badge.dataset.count = cartCount;
            } else {
                badge.style.display = 'none';
            }
        });
    }

    // Ghi đè các hàm thông báo để sử dụng thông báo đơn giản
    window.showNotificationRealtime = function (message, type) {
        return window.showSimpleNotification(message, type);
    };

    window.showNotification = function (message, type) {
        return window.showSimpleNotification(message, type);
    };

    // Hàm hiển thị thông báo đơn giản
    window.showSimpleNotification = function (message, type = 'success', duration = 5000) {
        console.log('Hiển thị thông báo đơn giản:', message, type);

        // Xóa thông báo cũ nếu có
        const oldNotification = document.getElementById('simple-notification');
        if (oldNotification) {
            oldNotification.remove();
        }

        // Tạo thông báo mới
        const notification = document.createElement('div');
        notification.id = 'simple-notification';
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.left = '50%';
        notification.style.transform = 'translateX(-50%)';
        notification.style.zIndex = '9999';
        notification.style.backgroundColor = type === 'success' ? '#10B981' : '#EF4444';
        notification.style.color = 'white';
        notification.style.padding = '12px 20px';
        notification.style.borderRadius = '8px';
        notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        notification.style.display = 'flex';
        notification.style.alignItems = 'center';
        notification.style.maxWidth = '90%';
        notification.style.width = 'auto';
        notification.style.animation = 'fadeIn 0.3s ease-out forwards';

        // Tạo nội dung thông báo
        notification.innerHTML = `
      <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}" style="margin-right: 10px;"></i>
      <span style="flex: 1;">${message}</span>
      <button onclick="this.parentElement.remove();" style="background: none; border: none; color: white; cursor: pointer; margin-left: 10px;">
        <i class="fas fa-times"></i>
      </button>
    `;

        // Thêm thông báo vào body
        document.body.appendChild(notification);

        // Tự động đóng thông báo sau thời gian đã định
        setTimeout(function () {
            if (notification && notification.parentNode) {
                notification.remove();
            }
        }, duration);

        return notification;
    };

    // LOẠI BỎ event listener trùng lặp - đã được xử lý trong cart-realtime.js
    document.addEventListener('DOMContentLoaded', function () {
        // LOẠI BỎ - Bắt sự kiện nhấp vào nút "Thêm vào giỏ" đã được xử lý trong cart-realtime.js
        // const addToCartButtons = document.querySelectorAll('.quick-add-to-cart-btn, .add-to-cart-btn');
        // addToCartButtons.forEach(button => {
        //     button.addEventListener('click', function(e) {
        //         this.classList.add('button-clicked');
        //         setTimeout(() => {
        //             this.classList.remove('button-clicked');
        //         }, 200);
        //         if (typeof flyToCart === 'function') {
        //             flyToCart(this);
        //         }
        //     });
        // });

        // Đảm bảo nút thêm giỏ hàng và báo giá liên hệ hiển thị đúng
        function fixProductActionButtons() {
            // Đảm bảo nút thêm giỏ hàng hiển thị đúng
            document.querySelectorAll('.product-card .product-actions .add-to-cart-btn').forEach(button => {
                button.style.display = 'flex';
                button.style.opacity = '1';
                button.style.transform = 'none';
            });

            // Đảm bảo nút báo giá liên hệ hiển thị đúng
            document.querySelectorAll('.product-card .product-actions a[href*="contact.php"]').forEach(link => {
                link.style.display = 'flex';
                link.style.opacity = '1';
                link.style.transform = 'none';
            });

            // Đảm bảo cart badges được khởi tạo
            if (typeof window.initializeCartBadgesFromStorage === 'function') {
                window.initializeCartBadgesFromStorage();
            }
        }

        // Duplicate handleScrollAnimation function đã được xóa

        // Gọi hàm khi trang tải xong và sau khi cuộn trang
        fixProductActionButtons();
        window.addEventListener('scroll', fixProductActionButtons);

        // Duplicate animation initialization đã được xóa

        // Hiển thị thông báo khi thêm thành công vào giỏ hàng từ AJAX
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function () {
            this.addEventListener('load', function () {
                if (this.readyState === 4 && this.status === 200) {
                    try {
                        const url = this._url || this.responseURL;
                        if (url.includes('add_to_cart.php')) {
                            const response = JSON.parse(this.responseText);
                            if (response.success) {
                                // Sử dụng thông báo đơn giản
                                window.showSimpleNotification(response.message || 'Đã thêm sản phẩm vào giỏ hàng', 'success');
                            }
                        }
                    } catch (e) {
                        console.error('Lỗi xử lý phản hồi:', e);
                    }
                }
            });
            originalXHROpen.apply(this, arguments);
        };

        // Thêm cả hiệu ứng cho Fetch API
        const originalFetch = window.fetch;
        window.fetch = function () {
            return originalFetch.apply(this, arguments)
                .then(response => {
                    const url = arguments[0].toString();
                    if (url.includes('add_to_cart.php')) {
                        response.clone().json().then(data => {
                            if (data.success) {
                                // Đã được xử lý bởi sự kiện click, không cần hiển thị thông báo lần nữa
                            }
                        }).catch(() => { });
                    }
                    return response;
                });
        };
    });
</script>

<!-- Script khởi tạo slider sản phẩm nổi bật -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Duplicate handleScrollAnimations call đã được xóa

        // Tăng cường hiệu ứng cho tiêu đề danh mục
        enhanceCategoryHeaders();



        // Đảm bảo Swiper đã tải
        if (typeof Swiper === 'undefined') {
            console.error('Thư viện Swiper chưa được tải');
            return;
        }

        // Biến để theo dõi trạng thái tương tác của người dùng
        let userInteractionTimer = null;
        let isUserInteracting = false;
        let isModalOpen = false;
        let isVideoPlaying = false;
        let isFancyboxOpen = false;

        // Khởi tạo Swiper cho phần cảm nhận khách hàng với autoplay tắt ban đầu
        var testimonialSwiper = new Swiper('.testimonials-swiper', {
            slidesPerView: 1,
            spaceBetween: 20,
            grabCursor: true,
            loop: true,
            speed: 800,
            autoplay: false, // Tắt autoplay ban đầu
            pagination: {
                el: '.testimonials-pagination',
                clickable: true,
                dynamicBullets: true,
            },
            navigation: {
                nextEl: '.testimonials-button-next',
                prevEl: '.testimonials-button-prev',
            },
            breakpoints: {
                // Khi màn hình >= 640px
                640: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                },
                // Khi màn hình >= 1024px
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                }
            },
            on: {
                init: function () {
                    console.log('Swiper testimonials đã khởi tạo');
                },
                slideChange: function () {
                    console.log('Swiper slide changed');
                },
                // Dừng autoplay khi người dùng tương tác
                touchStart: function () {
                    handleUserInteraction();
                },
                slideChangeTransitionStart: function () {
                    // Chỉ dừng nếu là do người dùng tương tác (không phải autoplay)
                    if (this.touches && this.touches.startX) {
                        handleUserInteraction();
                    }
                }
            }
        });

        // Hàm xử lý khi người dùng tương tác
        function handleUserInteraction() {
            isUserInteracting = true;

            // Dừng autoplay nếu đang chạy
            if (testimonialSwiper && testimonialSwiper.autoplay.running) {
                testimonialSwiper.autoplay.stop();
                console.log('Đã dừng autoplay do người dùng tương tác');
            }

            // Clear timer cũ nếu có
            if (userInteractionTimer) {
                clearTimeout(userInteractionTimer);
            }

            // Đặt timer 5 giây để khởi động lại autoplay (chỉ nếu không có modal, video hoặc fancybox đang mở)
            userInteractionTimer = setTimeout(() => {
                // Kiểm tra xem có modal nào đang mở không
                const openModal = document.querySelector('.testimonial-videos-modal');
                const activeVideo = document.querySelector('video:not([paused])');
                const playingVideo = document.querySelector('video[autoplay], video:not([paused]):not([ended])');
                const videoIframes = document.querySelectorAll('.testimonial-video iframe');
                const allVideos = document.querySelectorAll('video');

                // Kiểm tra chi tiết hơn về video đang phát
                let hasPlayingVideo = false;
                allVideos.forEach(video => {
                    if (!video.paused && !video.ended && video.currentTime > 0) {
                        hasPlayingVideo = true;
                    }
                });

                // Kiểm tra iframe video trong testimonial cards
                let hasVideoIframe = videoIframes.length > 0;

                const fancyboxContainer = document.querySelector('.fancybox__container');
                const fancyboxSlide = document.querySelector('.fancybox__slide.is-selected');

                // Debug logging
                console.log('=== KIỂM TRA TRẠNG THÁI AUTOPLAY ===');
                console.log('openModal:', !!openModal);
                console.log('activeVideo:', !!activeVideo);
                console.log('playingVideo:', !!playingVideo);
                console.log('videoIframes:', videoIframes.length);
                console.log('hasVideoIframe:', hasVideoIframe);
                console.log('fancyboxContainer:', !!fancyboxContainer);
                console.log('fancyboxSlide:', !!fancyboxSlide);
                console.log('isModalOpen:', isModalOpen);
                console.log('isVideoPlaying:', isVideoPlaying);
                console.log('isFancyboxOpen:', isFancyboxOpen);
                console.log('isUserInteracting:', isUserInteracting);
                console.log('hasPlayingVideo:', hasPlayingVideo);

                // Kiểm tra chi tiết iframe video
                if (videoIframes.length > 0) {
                    console.log('🎬 Phát hiện iframe video trong testimonial cards:', videoIframes.length);
                    videoIframes.forEach((iframe, index) => {
                        console.log(`Iframe ${index}:`, iframe.src);
                    });
                }

                // Chỉ khởi động lại nếu không có modal, video, iframe và fancybox đang phát
                if (!openModal && !activeVideo && !playingVideo && !hasPlayingVideo && !hasVideoIframe &&
                    !fancyboxContainer && !fancyboxSlide && !isModalOpen && !isVideoPlaying && !isFancyboxOpen) {
                    isUserInteracting = false;

                    // Khởi động lại autoplay nếu testimonials section đang trong viewport
                    const testimonialsSection = document.querySelector('.testimonials-section');
                    if (testimonialsSection) {
                        const rect = testimonialsSection.getBoundingClientRect();
                        const isInViewport = rect.top < window.innerHeight && rect.bottom > 0;

                        if (isInViewport && testimonialSwiper && !testimonialSwiper.autoplay.running) {
                            testimonialSwiper.params.autoplay = {
                                delay: 5000,
                                disableOnInteraction: false
                            };
                            testimonialSwiper.autoplay.start();
                            console.log('Đã khởi động lại autoplay sau 5 giây không tương tác');
                        }
                    }
                } else {
                    console.log('Không khởi động autoplay vì vẫn có modal, video hoặc fancybox đang mở');
                    // Đặt lại timer để kiểm tra sau 5 giây nữa
                    handleUserInteraction();
                }
            }, 5000); // 5 giây
        }

        // Hàm kiểm tra xem có thể khởi động autoplay không
        function canStartAutoplay() {
            const openModal = document.querySelector('.testimonial-videos-modal');
            const activeVideo = document.querySelector('video:not([paused])');
            const playingVideo = document.querySelector('video[autoplay], video:not([paused]):not([ended])');
            const videoIframes = document.querySelectorAll('.testimonial-video iframe');
            const allVideos = document.querySelectorAll('video');
            const fancyboxContainer = document.querySelector('.fancybox__container');
            const fancyboxSlide = document.querySelector('.fancybox__slide.is-selected');
            const testimonialsSection = document.querySelector('.testimonials-section');

            // Kiểm tra chi tiết hơn về video đang phát
            let hasPlayingVideo = false;
            allVideos.forEach(video => {
                if (!video.paused && !video.ended && video.currentTime > 0) {
                    hasPlayingVideo = true;
                }
            });

            // Kiểm tra iframe video
            let hasVideoIframe = videoIframes.length > 0;

            if (!testimonialsSection) return false;

            const rect = testimonialsSection.getBoundingClientRect();
            const isInViewport = rect.top < window.innerHeight && rect.bottom > 0;

            return isInViewport &&
                !isUserInteracting &&
                !isModalOpen &&
                !isVideoPlaying &&
                !isFancyboxOpen &&
                !openModal &&
                !activeVideo &&
                !playingVideo &&
                !hasPlayingVideo &&
                !hasVideoIframe &&
                !fancyboxContainer &&
                !fancyboxSlide &&
                testimonialSwiper &&
                !testimonialSwiper.autoplay.running;
        }

        // Thêm event listeners cho navigation buttons và pagination
        document.addEventListener('DOMContentLoaded', function () {
            // Navigation buttons
            const nextBtn = document.querySelector('.testimonials-button-next');
            const prevBtn = document.querySelector('.testimonials-button-prev');
            const pagination = document.querySelector('.testimonials-pagination');

            if (nextBtn) {
                nextBtn.addEventListener('click', handleUserInteraction);
            }

            if (prevBtn) {
                prevBtn.addEventListener('click', handleUserInteraction);
            }

            if (pagination) {
                pagination.addEventListener('click', handleUserInteraction);
            }

            // Thêm event listeners cho các tương tác với testimonial cards
            const testimonialCards = document.querySelectorAll('.testimonial-card');
            testimonialCards.forEach(card => {
                // Hover events
                card.addEventListener('mouseenter', handleUserInteraction);

                // Click events để phát hiện click ra ngoài video
                card.addEventListener('click', function (e) {
                    // Nếu click không phải vào video area và có video đang phát
                    const videoContainer = card.querySelector('.testimonial-video');
                    const iframe = videoContainer?.querySelector('iframe');

                    if (iframe && !e.target.closest('.testimonial-video') && isVideoPlaying) {
                        console.log('🖱️ Click ra ngoài video area - có thể pause video');

                        // Đặt timer để reset video state sau 3 giây
                        setTimeout(() => {
                            if (isVideoPlaying) {
                                isVideoPlaying = false;
                                console.log('⏸️ Auto-pause video sau 3 giây click outside');
                            }
                        }, 3000);
                    }
                });

                // Click events cho video và ảnh
                const videoTriggers = card.querySelectorAll('.video-trigger');
                const photoLinks = card.querySelectorAll('.testimonial-photo-link');
                const moreVideosLinks = card.querySelectorAll('.more-videos-link');

                videoTriggers.forEach(trigger => {
                    trigger.addEventListener('click', function (e) {
                        isVideoPlaying = true;
                        console.log('🎬 Video trigger clicked - dừng autoplay ngay lập tức');

                        // Dừng autoplay ngay lập tức
                        if (testimonialSwiper && testimonialSwiper.autoplay.running) {
                            testimonialSwiper.autoplay.stop();
                            console.log('⏹️ Đã dừng autoplay do click video trigger');
                        }

                        // Clear timer hiện tại
                        if (userInteractionTimer) {
                            clearTimeout(userInteractionTimer);
                            userInteractionTimer = null;
                        }
                    });
                });

                photoLinks.forEach(link => {
                    link.addEventListener('click', function () {
                        isModalOpen = true;
                        handleUserInteraction();
                        console.log('Photo link clicked - dừng autoplay');
                    });
                });

                moreVideosLinks.forEach(link => {
                    link.addEventListener('click', function () {
                        isModalOpen = true;
                        handleUserInteraction();
                        console.log('More videos link clicked - dừng autoplay');
                    });
                });
            });

            // Observer để theo dõi khi modal được thêm vào DOM
            const bodyObserver = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    mutation.addedNodes.forEach(function (node) {
                        if (node.nodeType === 1) { // Element node
                            // Kiểm tra modal videos
                            if (node.classList && node.classList.contains('testimonial-videos-modal')) {
                                isModalOpen = true;
                                handleUserInteraction();
                                console.log('Modal videos được mở - dừng autoplay');

                                // Thêm event listener cho nút đóng modal
                                const closeBtn = node.querySelector('.close-modal');
                                if (closeBtn) {
                                    closeBtn.addEventListener('click', function () {
                                        isModalOpen = false;
                                        console.log('Modal đã đóng - có thể khởi động lại autoplay');
                                    });
                                }

                                // Thêm event listener cho click outside modal
                                node.addEventListener('click', function (e) {
                                    if (e.target === node) {
                                        isModalOpen = false;
                                        console.log('Modal đã đóng (click outside) - có thể khởi động lại autoplay');
                                    }
                                });
                            }

                            // Kiểm tra Fancybox container
                            if (node.classList && node.classList.contains('fancybox__container')) {
                                isFancyboxOpen = true;
                                handleUserInteraction();
                                console.log('Fancybox container được tạo - dừng autoplay');
                            }

                            // Kiểm tra iframe video trong testimonial cards
                            if (node.tagName === 'IFRAME' && node.closest('.testimonial-video')) {
                                isVideoPlaying = true;
                                console.log('🎬 Iframe video được tạo trong testimonial card - dừng autoplay');

                                // Dừng autoplay ngay lập tức
                                if (testimonialSwiper && testimonialSwiper.autoplay.running) {
                                    testimonialSwiper.autoplay.stop();
                                    console.log('⏹️ Đã dừng autoplay do iframe video được tạo');
                                }

                                // Thêm overlay để theo dõi click outside
                                const videoContainer = node.closest('.testimonial-video');
                                if (videoContainer && !videoContainer.querySelector('.video-overlay')) {
                                    const overlay = document.createElement('div');
                                    overlay.className = 'video-overlay';
                                    overlay.style.cssText = `
                                    position: absolute;
                                    top: -10px;
                                    left: -10px;
                                    right: -10px;
                                    bottom: -10px;
                                    z-index: -1;
                                    pointer-events: none;
                                `;
                                    videoContainer.style.position = 'relative';
                                    videoContainer.appendChild(overlay);

                                    // Thêm nút đóng video
                                    const closeBtn = document.createElement('button');
                                    closeBtn.className = 'video-close-btn';
                                    closeBtn.innerHTML = '×';
                                    closeBtn.style.cssText = `
                                    position: absolute;
                                    top: 5px;
                                    right: 5px;
                                    width: 30px;
                                    height: 30px;
                                    background: rgba(0,0,0,0.7);
                                    color: white;
                                    border: none;
                                    border-radius: 50%;
                                    cursor: pointer;
                                    z-index: 1000;
                                    font-size: 18px;
                                    line-height: 1;
                                `;

                                    closeBtn.addEventListener('click', function () {
                                        // Xóa iframe và reset video container
                                        const iframe = videoContainer.querySelector('iframe');
                                        if (iframe) {
                                            iframe.remove();
                                        }
                                        closeBtn.remove();
                                        overlay.remove();

                                        // Reset video container về trạng thái ban đầu
                                        const videoUrl = videoContainer.querySelector('.video-trigger')?.getAttribute('data-video-url');
                                        const thumbnailUrl = videoContainer.querySelector('.video-trigger')?.getAttribute('data-thumbnail');

                                        if (videoUrl) {
                                            videoContainer.innerHTML = `
                                            <a href="#" class="video-trigger" data-video-url="${videoUrl}">
                                                <div class="video-placeholder with-thumbnail" style="background-image: url('${thumbnailUrl}');">
                                                    <i class="fas fa-play-circle"></i>
                                                </div>
                                            </a>
                                        `;

                                            // Re-attach event listener
                                            const newTrigger = videoContainer.querySelector('.video-trigger');
                                            if (newTrigger) {
                                                newTrigger.addEventListener('click', function (e) {
                                                    e.preventDefault();
                                                    isVideoPlaying = true;
                                                    console.log('🎬 Video trigger clicked - dừng autoplay ngay lập tức');

                                                    if (testimonialSwiper && testimonialSwiper.autoplay.running) {
                                                        testimonialSwiper.autoplay.stop();
                                                        console.log('⏹️ Đã dừng autoplay do click video trigger');
                                                    }

                                                    if (userInteractionTimer) {
                                                        clearTimeout(userInteractionTimer);
                                                        userInteractionTimer = null;
                                                    }
                                                });
                                            }
                                        }

                                        isVideoPlaying = false;
                                        console.log('🔄 Video đã được đóng - reset isVideoPlaying');
                                    });

                                    videoContainer.appendChild(closeBtn);
                                }
                            }

                            // Kiểm tra video elements (bao gồm cả video trong testimonial cards)
                            if (node.tagName === 'VIDEO' || node.querySelector('video')) {
                                const videos = node.tagName === 'VIDEO' ? [node] : node.querySelectorAll('video');
                                videos.forEach(video => {
                                    console.log('Đã phát hiện video element mới:', video);

                                    video.addEventListener('play', function () {
                                        isVideoPlaying = true;
                                        handleUserInteraction();
                                        console.log('Video bắt đầu phát - dừng autoplay');
                                    });

                                    video.addEventListener('pause', function () {
                                        isVideoPlaying = false;
                                        console.log('Video đã dừng - có thể khởi động lại autoplay');
                                    });

                                    video.addEventListener('ended', function () {
                                        isVideoPlaying = false;
                                        console.log('Video đã kết thúc - có thể khởi động lại autoplay');
                                    });

                                    video.addEventListener('loadstart', function () {
                                        console.log('Video đang load');
                                    });

                                    video.addEventListener('canplay', function () {
                                        console.log('Video sẵn sàng phát');
                                    });
                                });
                            }
                        }
                    });

                    mutation.removedNodes.forEach(function (node) {
                        if (node.nodeType === 1) {
                            if (node.classList && node.classList.contains('testimonial-videos-modal')) {
                                isModalOpen = false;
                                console.log('Modal đã bị xóa khỏi DOM - có thể khởi động lại autoplay');
                            }
                            if (node.classList && node.classList.contains('fancybox__container')) {
                                isFancyboxOpen = false;
                                console.log('Fancybox container đã bị xóa khỏi DOM - có thể khởi động lại autoplay');
                            }

                            // Kiểm tra iframe video bị xóa
                            if (node.tagName === 'IFRAME' && node.closest && node.closest('.testimonial-video')) {
                                console.log('🗑️ Iframe video đã bị xóa - có thể khởi động lại autoplay');

                                // Kiểm tra xem còn iframe nào khác không
                                setTimeout(() => {
                                    const remainingIframes = document.querySelectorAll('.testimonial-video iframe');
                                    if (remainingIframes.length === 0) {
                                        isVideoPlaying = false;
                                        console.log('✅ Không còn iframe video nào - reset isVideoPlaying');
                                    }
                                }, 100);
                            }
                        }
                    });
                });
            });

            // Bắt đầu observe body để theo dõi modal
            bodyObserver.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Theo dõi video elements hiện có trong DOM
            function attachVideoListeners() {
                const existingVideos = document.querySelectorAll('video');
                existingVideos.forEach(video => {
                    // Kiểm tra xem đã có event listeners chưa
                    if (!video.hasAttribute('data-autoplay-listeners')) {
                        video.setAttribute('data-autoplay-listeners', 'true');

                        console.log('Đã gắn event listeners cho video hiện có:', video);

                        video.addEventListener('play', function () {
                            isVideoPlaying = true;
                            console.log('🎬 Video hiện có bắt đầu phát - dừng autoplay');

                            // Dừng autoplay ngay lập tức
                            if (testimonialSwiper && testimonialSwiper.autoplay.running) {
                                testimonialSwiper.autoplay.stop();
                                console.log('⏹️ Đã dừng autoplay do video phát');
                            }
                        });

                        video.addEventListener('pause', function () {
                            isVideoPlaying = false;
                            console.log('⏸️ Video hiện có đã dừng - có thể khởi động lại autoplay');
                        });

                        video.addEventListener('ended', function () {
                            isVideoPlaying = false;
                            console.log('🏁 Video hiện có đã kết thúc - có thể khởi động lại autoplay');
                        });

                        video.addEventListener('timeupdate', function () {
                            // Kiểm tra liên tục khi video đang phát
                            if (!video.paused && !video.ended && video.currentTime > 0) {
                                if (!isVideoPlaying) {
                                    isVideoPlaying = true;
                                    console.log('🔄 Phát hiện video đang phát qua timeupdate');

                                    // Dừng autoplay nếu đang chạy
                                    if (testimonialSwiper && testimonialSwiper.autoplay.running) {
                                        testimonialSwiper.autoplay.stop();
                                        console.log('⏹️ Đã dừng autoplay do phát hiện video đang phát');
                                    }
                                }
                            }
                        });
                    }
                });
            }

            // Gọi hàm khi DOM ready
            attachVideoListeners();

            // Gọi lại hàm mỗi 2 giây để catch video được tạo bằng JavaScript
            setInterval(attachVideoListeners, 2000);

            // Thêm event listener cho swiper slide change để reset video state
            if (testimonialSwiper) {
                testimonialSwiper.on('slideChange', function () {
                    if (isVideoPlaying) {
                        console.log('📱 Swiper slide changed - reset video state');
                        isVideoPlaying = false;
                    }
                });
            }

            // Thêm event listener cho scroll để phát hiện khi user scroll khỏi video
            let scrollTimeout;
            window.addEventListener('scroll', function () {
                if (isVideoPlaying) {
                    clearTimeout(scrollTimeout);
                    scrollTimeout = setTimeout(() => {
                        // Kiểm tra xem video có còn trong viewport không
                        const videoIframes = document.querySelectorAll('.testimonial-video iframe');
                        let videoInViewport = false;

                        videoIframes.forEach(iframe => {
                            const rect = iframe.getBoundingClientRect();
                            if (rect.top < window.innerHeight && rect.bottom > 0) {
                                videoInViewport = true;
                            }
                        });

                        if (!videoInViewport && isVideoPlaying) {
                            console.log('📜 Video scrolled out of viewport - reset video state');
                            isVideoPlaying = false;
                        }
                    }, 1000); // Đợi 1 giây sau khi scroll dừng
                }
            });

            // Biến để theo dõi thời gian video được tạo
            let videoStartTime = null;

            // Kiểm tra liên tục iframe video mỗi 3 giây
            setInterval(() => {
                const videoIframes = document.querySelectorAll('.testimonial-video iframe');
                const allVideos = document.querySelectorAll('video');
                let foundPlayingContent = false;

                // Kiểm tra iframe video
                if (videoIframes.length > 0) {
                    foundPlayingContent = true;
                    if (!isVideoPlaying) {
                        isVideoPlaying = true;
                        videoStartTime = Date.now();
                        console.log('🔍 Phát hiện iframe video qua interval check');

                        // Dừng autoplay nếu đang chạy
                        if (testimonialSwiper && testimonialSwiper.autoplay.running) {
                            testimonialSwiper.autoplay.stop();
                            console.log('⏹️ Đã dừng autoplay do phát hiện iframe video (interval)');
                        }
                    } else {
                        // Kiểm tra timeout - nếu video đã chạy quá 30 giây mà không có tương tác
                        if (videoStartTime && (Date.now() - videoStartTime) > 30000) {
                            console.log('⏰ Video timeout sau 30 giây - có thể user đã pause');
                            isVideoPlaying = false;
                            videoStartTime = null;
                        }
                    }
                }

                // Kiểm tra video elements
                allVideos.forEach(video => {
                    if (!video.paused && !video.ended && video.currentTime > 0) {
                        foundPlayingContent = true;
                        if (!isVideoPlaying) {
                            isVideoPlaying = true;
                            console.log('🔍 Phát hiện video element đang phát qua interval check');

                            // Dừng autoplay nếu đang chạy
                            if (testimonialSwiper && testimonialSwiper.autoplay.running) {
                                testimonialSwiper.autoplay.stop();
                                console.log('⏹️ Đã dừng autoplay do phát hiện video đang phát (interval)');
                            }
                        }
                    }
                });

                // Nếu không có video/iframe nào đang phát và isVideoPlaying = true, reset lại
                if (!foundPlayingContent && isVideoPlaying) {
                    isVideoPlaying = false;
                    videoStartTime = null;
                    console.log('🔄 Reset isVideoPlaying = false vì không có video/iframe nào đang phát');
                }
            }, 3000); // Kiểm tra mỗi 3 giây
        });



        // Sử dụng Intersection Observer để theo dõi khi phần cảm nhận khách hàng xuất hiện trong viewport
        const testimonialsSection = document.querySelector('.testimonials-section');
        if (testimonialsSection) {
            const testimonialsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Khi phần tử nằm trong viewport, kích hoạt autoplay (chỉ nếu không có tương tác, modal, video hoặc fancybox)
                        if (testimonialSwiper && !testimonialSwiper.autoplay.running && !isUserInteracting && !isModalOpen && !isVideoPlaying && !isFancyboxOpen) {
                            // Cấu hình autoplay
                            testimonialSwiper.params.autoplay = {
                                delay: 5000,
                                disableOnInteraction: false
                            };
                            testimonialSwiper.autoplay.start();
                            console.log('Đã kích hoạt autoplay cho cảm nhận khách hàng');
                        }
                    } else {
                        // Khi phần tử không nằm trong viewport, dừng autoplay
                        if (testimonialSwiper && testimonialSwiper.autoplay.running) {
                            testimonialSwiper.autoplay.stop();
                            console.log('Đã dừng autoplay cho cảm nhận khách hàng');
                        }

                        // Clear timer khi ra khỏi viewport
                        if (userInteractionTimer) {
                            clearTimeout(userInteractionTimer);
                            userInteractionTimer = null;
                        }
                        isUserInteracting = false;
                        isModalOpen = false;
                        isVideoPlaying = false;
                        isFancyboxOpen = false;
                    }
                });
            }, {
                root: null,
                rootMargin: '0px',
                threshold: 0.1 // Kích hoạt khi 10% phần tử xuất hiện
            });

            testimonialsObserver.observe(testimonialsSection);
        }

        // Khởi tạo Swiper cho sản phẩm nổi bật với autoplay tắt ban đầu
        var featuredProductsSwiper = new Swiper('.featured-swiper', {
            slidesPerView: 1.2, // Hiển thị 1.2 card trên điện thoại
            spaceBetween: 15,
            grabCursor: true,
            centeredSlides: false,
            loop: true,
            speed: 800,
            autoplay: false, // Tắt autoplay ban đầu
            pagination: {
                el: '.featured-slider-pagination',
                clickable: true,
                bulletClass: 'w-3 h-3 bg-gray-300 rounded-full mx-1 cursor-pointer transition-all duration-300 opacity-70 inline-block',
                bulletActiveClass: 'w-4 h-4 bg-blue-500 opacity-100',
            },
            navigation: {
                nextEl: '.featured-button-next',
                prevEl: '.featured-button-prev',
            },
            breakpoints: {
                480: {
                    slidesPerView: 2,
                    spaceBetween: 15,
                },
                640: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                    centeredSlides: false,
                },
                768: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                    centeredSlides: false,
                },
                1024: {
                    slidesPerView: 4,
                    spaceBetween: 30,
                    centeredSlides: false,
                },
            },
            on: {
                init: function () {
                    console.log('Swiper đã khởi tạo');
                    // Kích hoạt hiệu ứng xuất hiện cho sản phẩm nổi bật
                    animateProductCards();
                },
                resize: function () {
                    // Kiểm tra kích thước màn hình và điều chỉnh hiệu ứng
                    if (window.innerWidth > 767) {
                        this.slides.forEach(slide => {
                            slide.style.opacity = '1';
                            slide.style.transform = 'scale(1)';
                        });
                    }
                }
            }
        });

        // Sử dụng Intersection Observer để theo dõi khi phần sản phẩm nổi bật xuất hiện trong viewport
        const featuredProductsSection = document.querySelector('.featured-products-slider');
        if (featuredProductsSection) {
            // Biến để theo dõi trạng thái tương tác
            let isUserInteracting = false;
            let interactionTimer = null;

            // Hàm xử lý khi người dùng bắt đầu tương tác
            const handleUserInteractionStart = () => {
                isUserInteracting = true;
                clearTimeout(interactionTimer);

                // Dừng autoplay khi người dùng tương tác
                if (featuredProductsSwiper && featuredProductsSwiper.autoplay.running) {
                    featuredProductsSwiper.autoplay.stop();
                    console.log('Đã dừng autoplay do người dùng tương tác');
                }
            };

            // Hàm xử lý khi người dùng kết thúc tương tác
            const handleUserInteractionEnd = () => {
                clearTimeout(interactionTimer);
                isUserInteracting = false;

                // Kiểm tra xem phần tử có trong viewport không
                const rect = featuredProductsSection.getBoundingClientRect();
                const isInViewport = (
                    rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
                    rect.bottom >= 0
                );

                // Tiếp tục autoplay ngay lập tức nếu phần tử trong viewport
                if (isInViewport && !isUserInteracting && featuredProductsSwiper) {
                    // Cấu hình autoplay với delay 5s
                    featuredProductsSwiper.params.autoplay = {
                        delay: 5000,
                        disableOnInteraction: false,
                        pauseOnMouseEnter: true
                    };
                    featuredProductsSwiper.autoplay.start();
                    console.log('Đã tiếp tục autoplay ngay lập tức với delay 5s');
                }
            };

            // Thêm sự kiện cho tất cả các thẻ sản phẩm
            const productCards = featuredProductsSection.querySelectorAll('.swiper-slide');
            productCards.forEach(card => {
                // Sự kiện chuột
                card.addEventListener('mouseenter', handleUserInteractionStart);
                card.addEventListener('mouseleave', handleUserInteractionEnd);

                // Sự kiện chạm (cho thiết bị di động)
                card.addEventListener('touchstart', handleUserInteractionStart, { passive: true });
                card.addEventListener('touchend', handleUserInteractionEnd);
            });

            // Thêm sự kiện cho nút điều hướng
            const navigationButtons = featuredProductsSection.querySelectorAll('.featured-button-next, .featured-button-prev');
            navigationButtons.forEach(button => {
                button.addEventListener('click', () => {
                    handleUserInteractionStart();
                    handleUserInteractionEnd();
                });
            });

            // Sử dụng Intersection Observer để theo dõi khi phần tử xuất hiện trong viewport
            const featuredObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Khi phần tử nằm trong viewport và không có tương tác, kích hoạt autoplay
                        if (featuredProductsSwiper && !featuredProductsSwiper.autoplay.running && !isUserInteracting) {
                            // Cấu hình autoplay với delay 5s
                            featuredProductsSwiper.params.autoplay = {
                                delay: 5000,
                                disableOnInteraction: false,
                                pauseOnMouseEnter: true
                            };
                            featuredProductsSwiper.autoplay.start();
                            console.log('Đã kích hoạt autoplay cho sản phẩm nổi bật với delay 5s');
                        }
                    } else {
                        // Khi phần tử không nằm trong viewport, dừng autoplay
                        if (featuredProductsSwiper && featuredProductsSwiper.autoplay.running) {
                            featuredProductsSwiper.autoplay.stop();
                            console.log('Đã dừng autoplay vì phần tử không trong viewport');
                        }
                    }
                });
            }, {
                root: null,
                rootMargin: '0px',
                threshold: 0.1 // Kích hoạt khi 10% phần tử xuất hiện
            });

            featuredObserver.observe(featuredProductsSection);
        }

        console.log('Đã thiết lập Swiper cho sản phẩm nổi bật');

        // Khởi tạo Swiper cho từng danh mục sản phẩm
        document.querySelectorAll('.category-swiper-container').forEach(function (container) {
            const categoryId = container.dataset.categoryId;

            // Khởi tạo Swiper với autoplay tắt ban đầu
            const categorySwiper = new Swiper(`.category-swiper-${categoryId}`, {
                slidesPerView: 1.2, // Hiển thị 1.2 card trên điện thoại
                spaceBetween: 15,
                grabCursor: true,
                centeredSlides: false, // Mặc định không căn giữa
                loop: true, // Bật loop để có thể chạy vô hạn
                speed: 800,
                autoplay: false, // Tắt autoplay ban đầu
                navigation: {
                    nextEl: `.category-button-next-${categoryId}`,
                    prevEl: `.category-button-prev-${categoryId}`,
                },
                pagination: {
                    el: `.category-slider-pagination-${categoryId}`,
                    clickable: true,
                    bulletClass: 'w-2.5 h-2.5 bg-gray-300 rounded-full mx-1 cursor-pointer transition-all duration-300 opacity-70 inline-block',
                    bulletActiveClass: 'w-3.5 h-3.5 bg-blue-500 opacity-100',
                },
                // Đảm bảo hiệu ứng đồng đều trên desktop
                on: {
                    resize: function () {
                        // Kiểm tra kích thước màn hình và điều chỉnh hiệu ứng
                        if (window.innerWidth > 767) {
                            this.slides.forEach(slide => {
                                slide.style.opacity = '1';
                                slide.style.transform = 'scale(1)';
                            });
                        }
                    },
                    init: function () {
                        // Đảm bảo hiệu ứng đúng khi khởi tạo
                        if (window.innerWidth > 767) {
                            this.slides.forEach(slide => {
                                slide.style.opacity = '1';
                                slide.style.transform = 'scale(1)';
                            });
                        }
                    }
                },
                breakpoints: {
                    480: {
                        slidesPerView: 2,
                        spaceBetween: 15,
                    },
                    640: {
                        slidesPerView: 2,
                        spaceBetween: 20,
                    },
                    768: {
                        slidesPerView: 3,
                        spaceBetween: 20,
                    },
                    1024: {
                        slidesPerView: 4,
                        spaceBetween: 30,
                    },
                }
            });

            // Biến để theo dõi trạng thái tương tác
            let isUserInteracting = false;
            let interactionTimer = null;

            // Hàm xử lý khi người dùng bắt đầu tương tác
            const handleUserInteractionStart = () => {
                isUserInteracting = true;
                clearTimeout(interactionTimer);

                // Dừng autoplay khi người dùng tương tác
                if (categorySwiper && categorySwiper.autoplay.running) {
                    categorySwiper.autoplay.stop();
                    console.log(`Đã dừng autoplay cho danh mục ${categoryId} do người dùng tương tác`);
                }
            };

            // Hàm xử lý khi người dùng kết thúc tương tác
            const handleUserInteractionEnd = () => {
                clearTimeout(interactionTimer);
                isUserInteracting = false;

                // Kiểm tra xem phần tử có trong viewport không
                const rect = container.getBoundingClientRect();
                const isInViewport = (
                    rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
                    rect.bottom >= 0
                );

                // Tiếp tục autoplay ngay lập tức nếu phần tử trong viewport
                if (isInViewport && !isUserInteracting && categorySwiper) {
                    // Cấu hình autoplay với delay 5s
                    categorySwiper.params.autoplay = {
                        delay: 5000,
                        disableOnInteraction: false,
                        pauseOnMouseEnter: true
                    };
                    categorySwiper.autoplay.start();
                    console.log(`Đã tiếp tục autoplay ngay lập tức cho danh mục ${categoryId} với delay 5s`);
                }
            };

            // Thêm sự kiện cho tất cả các thẻ sản phẩm
            const productCards = container.querySelectorAll('.swiper-slide');
            productCards.forEach(card => {
                // Sự kiện chuột
                card.addEventListener('mouseenter', handleUserInteractionStart);
                card.addEventListener('mouseleave', handleUserInteractionEnd);

                // Sự kiện chạm (cho thiết bị di động)
                card.addEventListener('touchstart', handleUserInteractionStart, { passive: true });
                card.addEventListener('touchend', handleUserInteractionEnd);
            });

            // Thêm sự kiện cho nút điều hướng
            const navigationButtons = container.querySelectorAll(`.category-button-next-${categoryId}, .category-button-prev-${categoryId}`);
            navigationButtons.forEach(button => {
                button.addEventListener('click', () => {
                    handleUserInteractionStart();
                    handleUserInteractionEnd();
                });
            });

            // Sử dụng Intersection Observer để theo dõi khi phần danh mục sản phẩm xuất hiện trong viewport
            const categoryObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Khi phần tử nằm trong viewport và không có tương tác, kích hoạt autoplay
                        if (categorySwiper && !categorySwiper.autoplay.running && !isUserInteracting) {
                            // Cấu hình autoplay
                            categorySwiper.params.autoplay = {
                                delay: 5000,
                                disableOnInteraction: false,
                                pauseOnMouseEnter: true
                            };
                            categorySwiper.autoplay.start();
                            console.log(`Đã kích hoạt autoplay cho danh mục ${categoryId}`);
                        }
                    } else {
                        // Khi phần tử không nằm trong viewport, dừng autoplay
                        if (categorySwiper && categorySwiper.autoplay.running) {
                            categorySwiper.autoplay.stop();
                            console.log(`Đã dừng autoplay cho danh mục ${categoryId}`);
                        }
                    }
                });
            }, {
                root: null,
                rootMargin: '0px',
                threshold: 0.1 // Kích hoạt khi 10% phần tử xuất hiện
            });

            // Bắt đầu theo dõi phần tử
            categoryObserver.observe(container);

            console.log(`Đã thiết lập Swiper cho danh mục ${categoryId}`);
        });

        // Thêm hiệu ứng cuộn xuất hiện cho các card
        function animateProductCards() {
            const productCards = document.querySelectorAll('.featured-products-slider .product-card');
            console.log('Số lượng product cards:', productCards.length);

            productCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100 * index);
            });
        }

        // Duplicate handleScrollAnimations function đã được xóa - sử dụng animation system thống nhất

        // Hiệu ứng cho tiêu đề danh mục
        function enhanceCategoryHeaders() {
            const categoryHeaders = document.querySelectorAll('.category-header-container');

            categoryHeaders.forEach(header => {
                // Thêm hiệu ứng hover cho icon
                const icon = header.querySelector('.category-icon');
                if (icon) {
                    icon.addEventListener('mouseenter', () => {
                        const glow = icon.querySelector('.category-icon-glow');
                        if (glow) {
                            glow.style.opacity = '1';
                            glow.style.transform = 'scale(1.2)';
                        }
                    });

                    icon.addEventListener('mouseleave', () => {
                        const glow = icon.querySelector('.category-icon-glow');
                        if (glow) {
                            glow.style.opacity = '0';
                            glow.style.transform = 'scale(0.8)';
                        }
                    });
                }
            });
        }

        // LOẠI BỎ - Xử lý sự kiện click cho nút thêm vào giỏ hàng nhanh đã được chuyển sang cart-realtime.js
        // để tránh xung đột và đảm bảo logic xử lý thống nhất
    });
</script>

<!-- Script cho phần Dịch vụ thiết kế -->
<script src="<?php echo BASE_URL; ?>/assets/js/design-services.js"></script>

<!-- Script cho phần Giới thiệu công ty -->
<script src="<?php echo BASE_URL; ?>/assets/js/company-intro.js"></script>

<!-- Script cho phần Cảm nhận khách hàng -->
<script src="<?php echo BASE_URL; ?>/assets/js/testimonials.js"></script>

<!-- Script cho Fancybox -->
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>
<script>
    // Khởi tạo Fancybox
    document.addEventListener('DOMContentLoaded', function () {
        console.log('Initializing Fancybox...');

        // Tìm tất cả các phần tử có data-fancybox
        const fancyboxElements = document.querySelectorAll('[data-fancybox]');
        console.log('Found', fancyboxElements.length, 'fancybox elements');

        Fancybox.bind('[data-fancybox]', {
            // Tùy chọn cơ bản
            dragToClose: false,
            Toolbar: {
                display: ["zoom", "slideshow", "fullscreen", "close"]
            },
            Thumbs: {
                autoStart: true
            },
            // Event callbacks để theo dõi trạng thái
            on: {
                init: function () {
                    console.log('Fancybox đã khởi tạo');
                },
                reveal: function () {
                    isFancyboxOpen = true;
                    handleUserInteraction();
                    console.log('Fancybox đã mở - dừng autoplay');
                },
                destroy: function () {
                    isFancyboxOpen = false;
                    console.log('Fancybox đã đóng - có thể khởi động lại autoplay');
                }
            }
        });

        // Thêm sự kiện click trực tiếp để đảm bảo Fancybox được kích hoạt
        fancyboxElements.forEach(function (element) {
            element.addEventListener('click', function (e) {
                console.log('Fancybox element clicked:', this);
                e.preventDefault();

                const galleryName = this.dataset.fancybox;
                const galleryItems = Array.from(document.querySelectorAll(`[data-fancybox="${galleryName}"]`));
                const index = galleryItems.indexOf(this);

                console.log('Opening gallery:', galleryName, 'at index:', index);

                Fancybox.show(
                    galleryItems.map(item => ({
                        src: item.href,
                        thumb: item.querySelector('img')?.src || item.href
                    })),
                    {
                        startIndex: index,
                        Thumbs: {
                            autoStart: true
                        }
                    }
                );
            });
        });
    });


</script>

<!-- Script animation system đơn giản và hiệu quả -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Hệ thống animation mượt mà giống testimonials
        const initSmoothCardAnimations = () => {
            // Thiết lập trạng thái ban đầu cho tất cả cards
            document.querySelectorAll('.fade-in-up').forEach((card, index) => {
                // Chỉ áp dụng cho handbook-card với hiệu ứng mượt mà
                if (card.classList.contains('handbook-card')) {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px) scale(0.95)';
                    card.style.transition = 'opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                } else {
                    // Các phần tử khác giữ nguyên
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                }
            });

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const swiperWrapper = element.closest('.swiper-wrapper');

                        if (swiperWrapper && !swiperWrapper.dataset.animated) {
                            // Đánh dấy wrapper đã được animate
                            swiperWrapper.dataset.animated = 'true';

                            // Lấy tất cả cards hiện đang visible (desktop: 4 cards, mobile: 1-2 cards)
                            const allCards = Array.from(swiperWrapper.querySelectorAll('.fade-in-up'));
                            const visibleCount = window.innerWidth >= 1024 ? 4 : (window.innerWidth >= 768 ? 3 : 2);
                            const visibleCards = allCards.slice(0, visibleCount);

                            // Animate với stagger effect mượt mà
                            visibleCards.forEach((card, index) => {
                                setTimeout(() => {
                                    card.style.opacity = '1';
                                    card.style.transform = 'translateY(0)';
                                    card.classList.add('visible');
                                }, index * 120); // Tăng delay để mượt hơn
                            });

                            // Sau khi animation hoàn thành, đánh dấu tất cả cards là visible để tránh hiệu ứng khi slide
                            setTimeout(() => {
                                allCards.forEach(card => {
                                    card.style.opacity = '1';
                                    card.style.transform = 'translateY(0)';
                                    card.classList.add('visible');
                                });
                            }, visibleCount * 120 + 100);

                            // Unobserve tất cả cards trong wrapper
                            allCards.forEach(card => observer.unobserve(card));
                        } else {
                            // Kiểm tra nếu là category grid
                            const categoryGrid = element.closest('.category-grid');

                            if (categoryGrid && !categoryGrid.dataset.animated) {
                                // Đánh dấu category grid đã được animate
                                categoryGrid.dataset.animated = 'true';

                                // Lấy tất cả category cards
                                const categoryCards = Array.from(categoryGrid.querySelectorAll('.fade-in-up'));

                                // Animate với stagger effect mượt mà
                                categoryCards.forEach((card, index) => {
                                    setTimeout(() => {
                                        card.style.opacity = '1';
                                        card.style.transform = 'translateY(0)';
                                        card.classList.add('visible');
                                    }, index * 120); // Delay mượt mà
                                });

                                // Unobserve tất cả category cards
                                categoryCards.forEach(card => observer.unobserve(card));
                            } else {
                                // Kiểm tra nếu là handbook card
                                const handbookGrid = element.closest('.handbook-grid');

                                if (handbookGrid && element.classList.contains('handbook-card')) {
                                    // Kiểm tra xem grid đã được animate chưa
                                    if (!handbookGrid.dataset.animated) {
                                        // Đánh dấu handbook grid đã được animate
                                        handbookGrid.dataset.animated = 'true';

                                        // Lấy tất cả handbook cards trong grid này
                                        const handbookCards = Array.from(handbookGrid.querySelectorAll('.handbook-card.fade-in-up'));

                                        // Animate với stagger effect mượt mà như thẻ sản phẩm
                                        handbookCards.forEach((card, index) => {
                                            setTimeout(() => {
                                                card.style.opacity = '1';
                                                card.style.transform = 'translateY(0) scale(1)';
                                                card.classList.add('visible');
                                            }, 100 + (index * 120)); // Delay ban đầu 100ms + 120ms giữa các card
                                        });

                                        // Unobserve tất cả handbook cards
                                        handbookCards.forEach(card => observer.unobserve(card));
                                    }
                                } else if (!swiperWrapper && !categoryGrid && !handbookGrid) {
                                    // Không phải slider, category grid hoặc handbook grid - animate đơn lẻ
                                    setTimeout(() => {
                                        element.style.opacity = '1';
                                        element.style.transform = 'translateY(0)';
                                        element.classList.add('visible');
                                    }, 50);
                                    observer.unobserve(element);
                                }
                            }
                        }
                    }
                });
            }, {
                root: null,
                rootMargin: '0px 0px -100px 0px',
                threshold: 0.1
            });

            // Observe tất cả fade-in-up elements
            document.querySelectorAll('.fade-in-up').forEach(element => {
                observer.observe(element);
            });
        };

        // Reset tất cả animation states
        document.querySelectorAll('.swiper-wrapper').forEach(wrapper => {
            delete wrapper.dataset.animated;
        });

        // Reset category grid animation state
        document.querySelectorAll('.category-grid').forEach(grid => {
            delete grid.dataset.animated;
        });

        // Reset handbook grid animation state
        document.querySelectorAll('.handbook-grid').forEach(grid => {
            delete grid.dataset.animated;
        });

        // Khởi tạo animation system mượt mà
        initSmoothCardAnimations();
    });
</script>

<!-- Script xử lý slide change events -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Đợi Swiper được khởi tạo
        setTimeout(() => {
            // Xử lý cho tất cả swipers
            document.querySelectorAll('.featured-swiper, .category-swiper-container').forEach(swiperElement => {
                if (swiperElement.swiper) {
                    swiperElement.swiper.on('slideChange', function() {
                        // Hiển thị ngay tất cả thẻ khi chuyển slide (không có hiệu ứng)
                        const allCards = swiperElement.querySelectorAll('.fade-in-up');
                        allCards.forEach(card => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                            card.classList.add('visible');
                        });
                    });
                }
            });
        }, 1000);
    });
</script>

<?php include_once 'partials/footer.php'; ?>