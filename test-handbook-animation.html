<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Handbook Animation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .spacer {
            height: 100vh;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 50px;
        }

        /* Handbook Grid */
        .handbook-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Article Card */
        .handbook-card {
            background: white;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 300px;
            display: flex;
            flex-direction: column;
        }

        .handbook-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
        }

        .handbook-card-image {
            height: 150px;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .handbook-card-content {
            padding: 1.5rem;
            flex-grow: 1;
        }

        .handbook-card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 0.75rem;
        }

        .handbook-card-excerpt {
            color: #6B7280;
            line-height: 1.6;
            font-size: 0.9rem;
        }

        /* Fade-in-up animation cho handbook cards */
        .handbook-card.fade-in-up {
            opacity: 0 !important;
            transform: translateY(30px) !important;
            transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
        }

        .handbook-card.fade-in-up.visible {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }

        @media (max-width: 768px) {
            .handbook-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spacer">
            Cuộn xuống để xem hiệu ứng handbook cards
        </div>

        <h2>Phần Cẩm Nang Nội Thất</h2>
        
        <!-- Articles Grid -->
        <div class="handbook-grid">
            <div class="handbook-card fade-in-up">
                <div class="handbook-card-image"></div>
                <div class="handbook-card-content">
                    <h3 class="handbook-card-title">Bài viết 1</h3>
                    <div class="handbook-card-excerpt">
                        Đây là nội dung mô tả cho bài viết đầu tiên trong cẩm nang nội thất.
                    </div>
                </div>
            </div>

            <div class="handbook-card fade-in-up">
                <div class="handbook-card-image"></div>
                <div class="handbook-card-content">
                    <h3 class="handbook-card-title">Bài viết 2</h3>
                    <div class="handbook-card-excerpt">
                        Đây là nội dung mô tả cho bài viết thứ hai trong cẩm nang nội thất.
                    </div>
                </div>
            </div>

            <div class="handbook-card fade-in-up">
                <div class="handbook-card-image"></div>
                <div class="handbook-card-content">
                    <h3 class="handbook-card-title">Bài viết 3</h3>
                    <div class="handbook-card-excerpt">
                        Đây là nội dung mô tả cho bài viết thứ ba trong cẩm nang nội thất.
                    </div>
                </div>
            </div>
        </div>

        <div class="spacer">
            Kết thúc test
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            console.log('DOM loaded, starting animation setup');
            
            // Thiết lập trạng thái ban đầu cho tất cả cards
            document.querySelectorAll('.fade-in-up').forEach((card, index) => {
                console.log('Setting up card', index, card);
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            });

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    console.log('Entry intersecting:', entry.isIntersecting, entry.target);
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const handbookGrid = element.closest('.handbook-grid');
                        
                        if (handbookGrid && element.classList.contains('handbook-card')) {
                            console.log('Handbook card detected:', element);
                            if (!handbookGrid.dataset.animated) {
                                console.log('Starting handbook grid animation');
                                handbookGrid.dataset.animated = 'true';

                                const handbookCards = Array.from(handbookGrid.querySelectorAll('.handbook-card.fade-in-up'));
                                console.log('Found handbook cards:', handbookCards.length);

                                handbookCards.forEach((card, index) => {
                                    setTimeout(() => {
                                        console.log('Animating card', index);
                                        card.style.opacity = '1';
                                        card.style.transform = 'translateY(0)';
                                        card.classList.add('visible');
                                    }, index * 150);
                                });

                                handbookCards.forEach(card => observer.unobserve(card));
                            }
                        }
                    }
                });
            }, {
                root: null,
                rootMargin: '0px 0px -100px 0px',
                threshold: 0.1
            });

            // Observe tất cả fade-in-up elements
            document.querySelectorAll('.fade-in-up').forEach(element => {
                console.log('Observing element:', element);
                observer.observe(element);
            });
        });
    </script>
</body>
</html>
