document.addEventListener('DOMContentLoaded', function() {
    // Hiệu ứng hiển thị khi cuộn trang - chỉ cho fade-in-up, không cho scroll-fade-in-up và handbook-card
    const fadeElements = document.querySelectorAll('.fade-in-up:not(.scroll-fade-in-up):not(.handbook-card)');

    // Hàm kiểm tra và hiển thị các phần tử
    const checkFadeElements = function() {
        fadeElements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementBottom = element.getBoundingClientRect().bottom;
            const isVisible = (elementTop < window.innerHeight - 50) && (elementBottom > 0);

            if (isVisible) {
                element.classList.add('visible');
            }
        });
    };

    // Kiểm tra ban đầu
    checkFadeElements();

    // Kiểm tra khi cuộn trang
    window.addEventListener('scroll', checkFadeElements);

    // Đảm bảo hình ảnh hiển thị đúng sau khi trang đã tải xong
    window.addEventListener('load', function() {
        console.log('Trang đã tải xong, kiểm tra hình ảnh sản phẩm');

        // Kiểm tra và sửa lỗi hiển thị hình ảnh ngay lập tức
        (function() {
            // Kiểm tra các phần tử simple-image
            document.querySelectorAll('.simple-image').forEach(img => {
                console.log('Tìm thấy simple-image:', img);

                // Đảm bảo background-image hiển thị đúng
                const style = window.getComputedStyle(img);
                const bgImage = style.backgroundImage;
                console.log('Background image:', bgImage);

                if (!bgImage || bgImage === 'none') {
                    console.log('Background image không hợp lệ, thử lấy từ thuộc tính style');
                    const inlineStyle = img.getAttribute('style');
                    console.log('Inline style:', inlineStyle);

                    // Thử sửa lỗi bằng cách thêm lại style
                    if (inlineStyle && inlineStyle.includes('background-image')) {
                        // Đã có style nhưng không hiển thị, thử thêm lại
                        img.style.backgroundSize = 'cover';
                        img.style.backgroundPosition = 'center';
                        img.style.position = 'absolute';
                        img.style.top = '0';
                        img.style.left = '0';
                        img.style.width = '100%';
                        img.style.height = '100%';
                        img.style.zIndex = '1';
                    }
                }
            });

            // Kiểm tra các phần tử img.product-image
            document.querySelectorAll('.product-image').forEach(img => {
                console.log('Tìm thấy product-image:', img);
                console.log('Src:', img.src);

                img.style.opacity = '1';
                img.style.visibility = 'visible';
                img.style.display = 'block';
            });

            // Thêm debug để kiểm tra URL hình ảnh
            document.querySelectorAll('.modern-product-card').forEach(card => {
                const productId = card.querySelector('.add-to-cart-btn')?.dataset.productId;
                const simpleImage = card.querySelector('.simple-image');

                if (simpleImage) {
                    const style = simpleImage.getAttribute('style');
                    console.log(`Product ID: ${productId}, Image style: ${style}`);

                    // Thử sửa lỗi bằng cách thêm lại style
                    simpleImage.style.backgroundSize = 'cover';
                    simpleImage.style.backgroundPosition = 'center';
                    simpleImage.style.position = 'absolute';
                    simpleImage.style.top = '0';
                    simpleImage.style.left = '0';
                    simpleImage.style.width = '100%';
                    simpleImage.style.height = '100%';
                    simpleImage.style.zIndex = '1';

                    // Dừng hiệu ứng loading
                    const productImage = card.querySelector('.simple-product-image');
                    if (productImage) {
                        productImage.style.animation = 'none';
                        productImage.style.backgroundImage = 'none';
                    }
                }
            });

            // Thực hiện lại kiểm tra sau 500ms để đảm bảo hình ảnh hiển thị đúng
            setTimeout(function() {
                document.querySelectorAll('.simple-image').forEach(img => {
                    // Đảm bảo background-image hiển thị đúng
                    img.style.backgroundSize = 'cover';
                    img.style.backgroundPosition = 'center';
                    img.style.position = 'absolute';
                    img.style.top = '0';
                    img.style.left = '0';
                    img.style.width = '100%';
                    img.style.height = '100%';
                    img.style.zIndex = '1';

                    // Dừng hiệu ứng loading cho container
                    const productImage = img.closest('.simple-product-image');
                    if (productImage) {
                        productImage.style.animation = 'none';
                        productImage.style.backgroundImage = 'none';
                    }
                });
            }, 500);
        })();
    });



    // Đảm bảo hình ảnh sản phẩm được tải đúng
    const productImages = document.querySelectorAll('.product-image');

    productImages.forEach(img => {
        // Xử lý lỗi tải hình ảnh
        img.addEventListener('error', function() {
            console.log('Lỗi tải hình ảnh:', this.src);
            const parent = this.parentElement;

            // Tạo phần tử thay thế
            const fallbackImage = document.createElement('div');
            fallbackImage.className = 'no-image';
            fallbackImage.innerHTML = '<i class="fas fa-image"></i>';

            // Thay thế hình ảnh bị lỗi
            this.style.display = 'none';
            parent.appendChild(fallbackImage);
        });

        // Đảm bảo hình ảnh đã tải xong
        if (img.complete) {
            console.log('Hình ảnh đã tải xong:', img.src);
            img.style.opacity = '1';
            const wrapper = img.closest('.product-image-wrapper');
            if (wrapper) {
                wrapper.style.animation = 'none';
                wrapper.style.backgroundImage = 'none';
            }
        } else {
            img.addEventListener('load', function() {
                console.log('Hình ảnh vừa tải xong:', this.src);
                this.style.opacity = '1';
                const wrapper = this.closest('.product-image-wrapper');
                if (wrapper) {
                    wrapper.style.animation = 'none';
                    wrapper.style.backgroundImage = 'none';
                }
            });
        }
    });

    // Kiểm tra và sửa lỗi hiển thị hình ảnh
    setTimeout(() => {
        document.querySelectorAll('.product-image-wrapper').forEach(wrapper => {
            const img = wrapper.querySelector('img.product-image');
            if (img) {
                // Đảm bảo hình ảnh hiển thị
                img.style.opacity = '1';
                img.style.visibility = 'visible';
                img.style.display = 'block';

                // Dừng animation loading
                wrapper.style.animation = 'none';
                wrapper.style.backgroundImage = 'none';
            }
        });
    }, 1000);

    // Hiệu ứng hover cho card sản phẩm
    const productCards = document.querySelectorAll('.modern-product-card');

    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('hovered');
        });

        card.addEventListener('mouseleave', function() {
            this.classList.remove('hovered');
        });
    });

    // LOẠI BỎ event listener trùng lặp - đã được xử lý trong cart-realtime.js
    // const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    // addToCartButtons.forEach(button => {
    //     button.addEventListener('click', function(e) {
    //         e.preventDefault();
    //         this.classList.add('clicked');
    //         const productId = this.getAttribute('data-product-id');
    //         setTimeout(() => {
    //             this.classList.remove('clicked');
    //         }, 300);
    //     });
    // });

// Banner slider đã được chuyển sang banner-slider.js để tránh xung đột

// Hàm tạo thông báo hiện đại
function showModernNotification(message, type = 'success', duration = 3000) {
    // Tính toán chiều cao của header
    const calculateHeaderHeight = () => {
        const header = document.querySelector('header');
        if (!header) return 20; // Giá trị mặc định nếu không tìm thấy header

        // Lấy chiều cao thực tế của header
        const headerHeight = header.offsetHeight;

        // Kiểm tra xem header có sticky không
        const isSticky = window.getComputedStyle(header).position === 'sticky' ||
                         window.getComputedStyle(header).position === 'fixed';

        // Thêm một khoảng cách nhỏ để đảm bảo thông báo không bị che khuất
        const padding = 20;

        return isSticky ? headerHeight + padding : padding;
    };

    // Tạo container thông báo nếu chưa tồn tại
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Điều chỉnh vị trí của container dựa trên chiều cao header
    const headerHeight = calculateHeaderHeight();
    container.style.top = `${headerHeight}px`;

    // Tạo thông báo mới
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Xác định icon dựa vào loại thông báo
    let icon = '';
    let title = '';

    switch (type) {
        case 'success':
            icon = 'fa-check-circle';
            title = 'Thành công';
            break;
        case 'error':
            icon = 'fa-exclamation-circle';
            title = 'Lỗi';
            break;
        case 'info':
            icon = 'fa-info-circle';
            title = 'Thông tin';
            break;
        case 'warning':
            icon = 'fa-exclamation-triangle';
            title = 'Cảnh báo';
            break;
        default:
            icon = 'fa-bell';
            title = 'Thông báo';
    }

    // Tùy chỉnh thông báo khi thêm vào giỏ hàng
    if (type === 'success' && (message.includes('giỏ hàng') || message.includes('Đã thêm'))) {
        // Tạo nội dung HTML cho thông báo thêm vào giỏ hàng với thiết kế cải tiến
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas ${icon}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-message">
                    <span class="font-medium">${message}</span>
                    <a href="${BASE_URL}/cart.php" class="text-primary hover:underline ml-1">Xem giỏ hàng</a>
                </div>
            </div>
            <div class="notification-progress">
                <div class="notification-progress-bar"></div>
            </div>
        `;

        // Thêm class đặc biệt cho thông báo giỏ hàng
        notification.classList.add('cart-notification');

        // Thời gian hiển thị cho thông báo thêm vào giỏ hàng
        duration = 3000;

        console.log('Hiển thị thông báo giỏ hàng với class cart-notification');
    } else {
        // Tạo nội dung HTML cho thông báo thông thường
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas ${icon}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">
                    ${title}
                    <span class="notification-close" onclick="closeNotification(this.parentElement.parentElement.parentElement)">
                        <i class="fas fa-times"></i>
                    </span>
                </div>
                <div class="notification-message">${message}</div>
            </div>
            <div class="notification-progress">
                <div class="notification-progress-bar"></div>
            </div>
        `;
    }

    // Thêm thông báo vào container
    container.appendChild(notification);

    // Hiển thị thông báo với hiệu ứng
    requestAnimationFrame(() => {
        notification.classList.add('show');
    });

    // Xóa thông báo sau thời gian đã định
    setTimeout(() => {
        closeNotification(notification);
    }, duration);

    // Thêm sự kiện resize để điều chỉnh vị trí khi kích thước màn hình thay đổi
    const handleResize = () => {
        const newHeaderHeight = calculateHeaderHeight();
        container.style.top = `${newHeaderHeight}px`;
    };

    // Đăng ký sự kiện resize nếu chưa đăng ký
    if (!window.notificationResizeHandlerAdded) {
        window.addEventListener('resize', handleResize);
        window.notificationResizeHandlerAdded = true;
    }

    return notification;
}

// Hàm đóng thông báo
function closeNotification(notification) {
    if (!notification) return;

    // Thêm animation đóng
    notification.classList.remove('show');
    notification.style.animation = 'slide-out 0.3s ease-out forwards';

    // Xóa thông báo sau khi animation kết thúc
    setTimeout(() => {
        notification.remove();
    }, 300);
}

// Hàm cập nhật tất cả các badge giỏ hàng
function updateCartBadges() {
    // Cố gắng cập nhật badge nếu có hàm updateCartCount
    if (typeof updateCartCount === 'function') {
        updateCartCount();
    }

    // Cập nhật số lượng giỏ hàng từ localStorage
    const cartCount = parseInt(localStorage.getItem('cartCount') || '0');

    // Cập nhật tất cả các badge
    document.querySelectorAll('.cart-badge, .mobile-nav-badge, .mobile-cart-badge').forEach(badge => {
        if (cartCount > 0) {
            badge.style.display = 'flex';
            badge.textContent = cartCount > 99 ? '99+' : cartCount;
            badge.dataset.count = cartCount;
        } else {
            badge.style.display = 'none';
        }
    });
}

// Ghi đè các hàm thông báo để sử dụng thông báo hiện đại
window.showNotificationRealtime = function(message, type) {
    return showModernNotification(message, type);
};

window.showNotification = function(message, type) {
    return showModernNotification(message, type);
};



// Script khởi tạo slider sản phẩm nổi bật - ĐÃ CHUYỂN SANG index.php
// Để tránh xung đột, tất cả Swiper initialization đã được chuyển sang index.php