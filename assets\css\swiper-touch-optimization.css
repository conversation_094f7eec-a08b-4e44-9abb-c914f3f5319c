/**
 * Swiper Touch Optimization CSS
 * Tối ưu hóa trải nghiệm vuốt chạm cho sliders
 */

/* ===== GLOBAL TOUCH OPTIMIZATION ===== */

/* Tắt highlight khi touch trên tất cả elements */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

/* Tối ưu cho tất cả swiper containers */
.swiper-container,
.swiper {
    touch-action: pan-y pinch-zoom;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
}

/* ===== SLIDE OPTIMIZATION ===== */

/* Tối ưu cho tất cả slides */
.swiper-slide {
    touch-action: manipulation;
    cursor: grab;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.swiper-slide:active {
    cursor: grabbing;
}

/* Ngăn selection cho tất cả elements trong slide */
.swiper-slide * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
}

/* ===== LINK OPTIMIZATION ===== */

/* Tối ưu cho links trong slides */
.swiper-slide a {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    display: block;
    position: relative;
}

/* Ngăn drag cho images trong links */
.swiper-slide a img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
    pointer-events: none;
}

/* ===== TOUCH DEVICE SPECIFIC ===== */

/* Tối ưu cho touch devices */
@media (hover: none) and (pointer: coarse) {
    .swiper-slide {
        cursor: default !important;
    }
    
    .swiper-slide:active {
        cursor: default !important;
    }
    
    /* Giảm sensitivity cho touch */
    .swiper-container,
    .swiper {
        touch-action: pan-x pan-y;
    }
    
    /* Tối ưu tap highlight */
    .swiper-slide a {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.05);
        tap-highlight-color: rgba(0, 0, 0, 0.05);
    }
    
    /* Tắt hover effects trên touch devices */
    .swiper-slide:hover {
        transform: none !important;
    }
}

/* ===== MOBILE SPECIFIC ===== */

@media (max-width: 768px) {
    /* Tối ưu touch cho mobile */
    .swiper-slide {
        touch-action: pan-x;
    }
    
    .swiper-slide a {
        display: block;
        -webkit-tap-highlight-color: transparent;
        min-height: 44px; /* Minimum touch target size */
    }
    
    /* Tăng kích thước touch target cho buttons */
    .swiper-button-next,
    .swiper-button-prev {
        min-width: 44px;
        min-height: 44px;
    }
    
    /* Tối ưu pagination */
    .swiper-pagination-bullet {
        min-width: 44px;
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
}

/* ===== PERFORMANCE OPTIMIZATION ===== */

/* GPU acceleration cho smooth scrolling */
.swiper-container,
.swiper,
.swiper-slide {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform;
}

/* Tối ưu transition */
.swiper-slide {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* ===== ACCESSIBILITY ===== */

/* Đảm bảo focus visible cho keyboard navigation */
.swiper-slide a:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Tắt outline khi click bằng chuột */
.swiper-slide a:focus:not(:focus-visible) {
    outline: none;
}

/* ===== SPECIFIC SLIDER OPTIMIZATION ===== */

/* Featured products slider */
.featured-products-slider .swiper-slide {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

/* Category products slider */
.category-products-slider .swiper-slide {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

/* ===== FALLBACK FOR OLDER BROWSERS ===== */

/* Fallback cho browsers không support touch-action */
@supports not (touch-action: manipulation) {
    .swiper-slide {
        -ms-touch-action: manipulation;
    }
}

/* Fallback cho browsers không support user-select */
@supports not (user-select: none) {
    .swiper-slide {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }
}

/* ===== DEBUG MODE (có thể bật khi cần debug) ===== */

/* Uncomment để debug touch areas
.swiper-slide {
    border: 1px dashed rgba(255, 0, 0, 0.3) !important;
}

.swiper-slide a {
    background: rgba(0, 255, 0, 0.1) !important;
}
*/
